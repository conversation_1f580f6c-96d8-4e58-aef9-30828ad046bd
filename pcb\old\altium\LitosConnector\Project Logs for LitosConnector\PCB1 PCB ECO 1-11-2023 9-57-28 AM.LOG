Added Component: Designator=1-39(MHDR1X20)
Add component (AddParameter): Name = "LatestRevisionDate"; Value = "17-Jul-2002"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "LatestRevisionNote"; Value = "Re-released for DXP Platform."; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Publisher"; Value = "Altium Limited"; VariantName = "[No Variations]"
Added Component: Designator=2-40(MHDR1X20)
Add component (AddParameter): Name = "LatestRevisionDate"; Value = "17-Jul-2002"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "LatestRevisionNote"; Value = "Re-released for DXP Platform."; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Publisher"; Value = "Altium Limited"; VariantName = "[No Variations]"
Added Component: Designator=41-59(MHDR1X10)
Add component (AddParameter): Name = "LatestRevisionDate"; Value = "17-Jul-2002"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "LatestRevisionNote"; Value = "Re-released for DXP Platform."; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Publisher"; Value = "Altium Limited"; VariantName = "[No Variations]"
Added Component: Designator=42-60(MHDR1X10)
Add component (AddParameter): Name = "LatestRevisionDate"; Value = "17-Jul-2002"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "LatestRevisionNote"; Value = "Re-released for DXP Platform."; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Publisher"; Value = "Altium Limited"; VariantName = "[No Variations]"
Added Component: Designator=61-79(MHDR1X10)
Add component (AddParameter): Name = "LatestRevisionDate"; Value = "17-Jul-2002"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "LatestRevisionNote"; Value = "Re-released for DXP Platform."; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Publisher"; Value = "Altium Limited"; VariantName = "[No Variations]"
Added Component: Designator=62-80(MHDR1X10)
Add component (AddParameter): Name = "LatestRevisionDate"; Value = "17-Jul-2002"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "LatestRevisionNote"; Value = "Re-released for DXP Platform."; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Publisher"; Value = "Altium Limited"; VariantName = "[No Variations]"
Added Component: Designator=81-119(MHDR1X20)
Add component (AddParameter): Name = "LatestRevisionDate"; Value = "17-Jul-2002"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "LatestRevisionNote"; Value = "Re-released for DXP Platform."; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Publisher"; Value = "Altium Limited"; VariantName = "[No Variations]"
Added Component: Designator=82-120(MHDR1X20)
Add component (AddParameter): Name = "LatestRevisionDate"; Value = "17-Jul-2002"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "LatestRevisionNote"; Value = "Re-released for DXP Platform."; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Publisher"; Value = "Altium Limited"; VariantName = "[No Variations]"
Added Component: Designator=J?(SAMTEC_ERF8-060-05.0-L-DV-L-TR)
Add component (AddParameter): Name = "Availability"; Value = "Unavailable"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "MF"; Value = ""; VariantName = "[No Variations]"
Add component (AddParameter): Name = "MP"; Value = "ERF8-060-05.0-L-DV-L-TR"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Package"; Value = "None"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Price"; Value = "None"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Value"; Value = ""; VariantName = "[No Variations]"
Added Component: Designator=J?(SAMTEC_ERF8-060-05.0-L-DV-L-TR)
Add component (AddParameter): Name = "Availability"; Value = "Unavailable"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "MF"; Value = ""; VariantName = "[No Variations]"
Add component (AddParameter): Name = "MP"; Value = "ERF8-060-05.0-L-DV-L-TR"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Package"; Value = "None"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Price"; Value = "None"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Value"; Value = ""; VariantName = "[No Variations]"
Added Pin To Net: NetName=Net1-39_1 Pin=1-39-1
Added Pin To Net: NetName=Net1-39_2 Pin=1-39-2
Added Pin To Net: NetName=Net1-39_3 Pin=1-39-3
Added Pin To Net: NetName=Net1-39_4 Pin=1-39-4
Added Pin To Net: NetName=Net1-39_5 Pin=1-39-5
Added Pin To Net: NetName=Net1-39_6 Pin=1-39-6
Added Pin To Net: NetName=Net1-39_7 Pin=1-39-7
Added Pin To Net: NetName=Net1-39_8 Pin=1-39-8
Added Pin To Net: NetName=Net1-39_9 Pin=1-39-9
Added Pin To Net: NetName=Net1-39_10 Pin=1-39-10
Added Pin To Net: NetName=Net1-39_11 Pin=1-39-11
Added Pin To Net: NetName=Net1-39_12 Pin=1-39-12
Added Pin To Net: NetName=Net1-39_13 Pin=1-39-13
Added Pin To Net: NetName=Net1-39_14 Pin=1-39-14
Added Pin To Net: NetName=Net1-39_15 Pin=1-39-15
Added Pin To Net: NetName=Net1-39_16 Pin=1-39-16
Added Pin To Net: NetName=Net1-39_17 Pin=1-39-17
Added Pin To Net: NetName=Net1-39_18 Pin=1-39-18
Added Pin To Net: NetName=Net1-39_19 Pin=1-39-19
Added Pin To Net: NetName=Net1-39_20 Pin=1-39-20
Added Pin To Net: NetName=Net2-40_1 Pin=2-40-1
Added Pin To Net: NetName=Net2-40_2 Pin=2-40-2
Added Pin To Net: NetName=Net2-40_3 Pin=2-40-3
Added Pin To Net: NetName=Net2-40_4 Pin=2-40-4
Added Pin To Net: NetName=Net2-40_5 Pin=2-40-5
Added Pin To Net: NetName=Net2-40_6 Pin=2-40-6
Added Pin To Net: NetName=Net2-40_7 Pin=2-40-7
Added Pin To Net: NetName=Net2-40_8 Pin=2-40-8
Added Pin To Net: NetName=Net2-40_9 Pin=2-40-9
Added Pin To Net: NetName=Net2-40_10 Pin=2-40-10
Added Pin To Net: NetName=Net2-40_11 Pin=2-40-11
Added Pin To Net: NetName=Net2-40_12 Pin=2-40-12
Added Pin To Net: NetName=Net2-40_13 Pin=2-40-13
Added Pin To Net: NetName=Net2-40_14 Pin=2-40-14
Added Pin To Net: NetName=Net2-40_15 Pin=2-40-15
Added Pin To Net: NetName=Net2-40_16 Pin=2-40-16
Added Pin To Net: NetName=Net2-40_17 Pin=2-40-17
Added Pin To Net: NetName=Net2-40_18 Pin=2-40-18
Added Pin To Net: NetName=Net2-40_19 Pin=2-40-19
Added Pin To Net: NetName=Net2-40_20 Pin=2-40-20
Added Pin To Net: NetName=Net41-59_1 Pin=41-59-1
Added Pin To Net: NetName=Net41-59_2 Pin=41-59-2
Added Pin To Net: NetName=Net41-59_3 Pin=41-59-3
Added Pin To Net: NetName=Net41-59_4 Pin=41-59-4
Added Pin To Net: NetName=Net41-59_5 Pin=41-59-5
Added Pin To Net: NetName=Net41-59_6 Pin=41-59-6
Added Pin To Net: NetName=Net41-59_7 Pin=41-59-7
Added Pin To Net: NetName=Net41-59_8 Pin=41-59-8
Added Pin To Net: NetName=Net41-59_9 Pin=41-59-9
Added Pin To Net: NetName=Net41-59_10 Pin=41-59-10
Added Pin To Net: NetName=Net42-60_1 Pin=42-60-1
Added Pin To Net: NetName=Net42-60_2 Pin=42-60-2
Added Pin To Net: NetName=Net42-60_3 Pin=42-60-3
Added Pin To Net: NetName=Net42-60_4 Pin=42-60-4
Added Pin To Net: NetName=Net42-60_5 Pin=42-60-5
Added Pin To Net: NetName=Net42-60_6 Pin=42-60-6
Added Pin To Net: NetName=Net42-60_7 Pin=42-60-7
Added Pin To Net: NetName=Net42-60_8 Pin=42-60-8
Added Pin To Net: NetName=Net42-60_9 Pin=42-60-9
Added Pin To Net: NetName=Net42-60_10 Pin=42-60-10
Added Pin To Net: NetName=Net61-79_1 Pin=61-79-1
Added Pin To Net: NetName=Net61-79_2 Pin=61-79-2
Added Pin To Net: NetName=Net61-79_3 Pin=61-79-3
Added Pin To Net: NetName=Net61-79_4 Pin=61-79-4
Added Pin To Net: NetName=Net61-79_5 Pin=61-79-5
Added Pin To Net: NetName=Net61-79_6 Pin=61-79-6
Added Pin To Net: NetName=Net61-79_7 Pin=61-79-7
Added Pin To Net: NetName=Net61-79_8 Pin=61-79-8
Added Pin To Net: NetName=Net61-79_9 Pin=61-79-9
Added Pin To Net: NetName=Net61-79_10 Pin=61-79-10
Added Pin To Net: NetName=Net62-80_1 Pin=62-80-1
Added Pin To Net: NetName=Net62-80_2 Pin=62-80-2
Added Pin To Net: NetName=Net62-80_3 Pin=62-80-3
Added Pin To Net: NetName=Net62-80_4 Pin=62-80-4
Added Pin To Net: NetName=Net62-80_5 Pin=62-80-5
Added Pin To Net: NetName=Net62-80_6 Pin=62-80-6
Added Pin To Net: NetName=Net62-80_7 Pin=62-80-7
Added Pin To Net: NetName=Net62-80_8 Pin=62-80-8
Added Pin To Net: NetName=Net62-80_9 Pin=62-80-9
Added Pin To Net: NetName=Net62-80_10 Pin=62-80-10
Added Pin To Net: NetName=Net81-119_1 Pin=81-119-1
Added Pin To Net: NetName=Net81-119_2 Pin=81-119-2
Added Pin To Net: NetName=Net81-119_3 Pin=81-119-3
Added Pin To Net: NetName=Net81-119_4 Pin=81-119-4
Added Pin To Net: NetName=Net81-119_5 Pin=81-119-5
Added Pin To Net: NetName=Net81-119_6 Pin=81-119-6
Added Pin To Net: NetName=Net81-119_7 Pin=81-119-7
Added Pin To Net: NetName=Net81-119_8 Pin=81-119-8
Added Pin To Net: NetName=Net81-119_9 Pin=81-119-9
Added Pin To Net: NetName=Net81-119_10 Pin=81-119-10
Added Pin To Net: NetName=Net81-119_11 Pin=81-119-11
Added Pin To Net: NetName=Net81-119_12 Pin=81-119-12
Added Pin To Net: NetName=Net81-119_13 Pin=81-119-13
Added Pin To Net: NetName=Net81-119_14 Pin=81-119-14
Added Pin To Net: NetName=Net81-119_15 Pin=81-119-15
Added Pin To Net: NetName=Net81-119_16 Pin=81-119-16
Added Pin To Net: NetName=Net81-119_17 Pin=81-119-17
Added Pin To Net: NetName=Net81-119_18 Pin=81-119-18
Added Pin To Net: NetName=Net81-119_19 Pin=81-119-19
Added Pin To Net: NetName=Net81-119_20 Pin=81-119-20
Added Pin To Net: NetName=Net82-120_1 Pin=82-120-1
Added Pin To Net: NetName=Net82-120_2 Pin=82-120-2
Added Pin To Net: NetName=Net82-120_3 Pin=82-120-3
Added Pin To Net: NetName=Net82-120_4 Pin=82-120-4
Added Pin To Net: NetName=Net82-120_5 Pin=82-120-5
Added Pin To Net: NetName=Net82-120_6 Pin=82-120-6
Added Pin To Net: NetName=Net82-120_7 Pin=82-120-7
Added Pin To Net: NetName=Net82-120_8 Pin=82-120-8
Added Pin To Net: NetName=Net82-120_9 Pin=82-120-9
Added Pin To Net: NetName=Net82-120_10 Pin=82-120-10
Added Pin To Net: NetName=Net82-120_11 Pin=82-120-11
Added Pin To Net: NetName=Net82-120_12 Pin=82-120-12
Added Pin To Net: NetName=Net82-120_13 Pin=82-120-13
Added Pin To Net: NetName=Net82-120_14 Pin=82-120-14
Added Pin To Net: NetName=Net82-120_15 Pin=82-120-15
Added Pin To Net: NetName=Net82-120_16 Pin=82-120-16
Added Pin To Net: NetName=Net82-120_17 Pin=82-120-17
Added Pin To Net: NetName=Net82-120_18 Pin=82-120-18
Added Pin To Net: NetName=Net82-120_19 Pin=82-120-19
Added Pin To Net: NetName=Net82-120_20 Pin=82-120-20
Added Pin To Net: NetName=Net1-39_20 Pin=J?-01
Added Pin To Net: NetName=Net2-40_1 Pin=J?-02
Added Pin To Net: NetName=Net1-39_19 Pin=J?-03
Added Pin To Net: NetName=Net2-40_2 Pin=J?-04
Added Pin To Net: NetName=Net1-39_18 Pin=J?-05
Added Pin To Net: NetName=Net2-40_3 Pin=J?-06
Added Pin To Net: NetName=Net1-39_17 Pin=J?-07
Added Pin To Net: NetName=Net2-40_4 Pin=J?-08
Added Pin To Net: NetName=Net1-39_16 Pin=J?-09
Added Pin To Net: NetName=Net2-40_5 Pin=J?-10
Added Pin To Net: NetName=Net1-39_15 Pin=J?-11
Added Pin To Net: NetName=Net2-40_6 Pin=J?-12
Added Pin To Net: NetName=Net1-39_14 Pin=J?-13
Added Pin To Net: NetName=Net2-40_7 Pin=J?-14
Added Pin To Net: NetName=Net1-39_13 Pin=J?-15
Added Pin To Net: NetName=Net2-40_8 Pin=J?-16
Added Pin To Net: NetName=Net1-39_12 Pin=J?-17
Added Pin To Net: NetName=Net2-40_9 Pin=J?-18
Added Pin To Net: NetName=Net1-39_11 Pin=J?-19
Added Pin To Net: NetName=Net2-40_10 Pin=J?-20
Added Pin To Net: NetName=Net1-39_10 Pin=J?-21
Added Pin To Net: NetName=Net2-40_11 Pin=J?-22
Added Pin To Net: NetName=Net1-39_9 Pin=J?-23
Added Pin To Net: NetName=Net2-40_12 Pin=J?-24
Added Pin To Net: NetName=Net1-39_8 Pin=J?-25
Added Pin To Net: NetName=Net2-40_13 Pin=J?-26
Added Pin To Net: NetName=Net1-39_7 Pin=J?-27
Added Pin To Net: NetName=Net2-40_14 Pin=J?-28
Added Pin To Net: NetName=Net1-39_6 Pin=J?-29
Added Pin To Net: NetName=Net2-40_15 Pin=J?-30
Added Pin To Net: NetName=Net1-39_5 Pin=J?-31
Added Pin To Net: NetName=Net2-40_16 Pin=J?-32
Added Pin To Net: NetName=Net1-39_4 Pin=J?-33
Added Pin To Net: NetName=Net2-40_17 Pin=J?-34
Added Pin To Net: NetName=Net1-39_3 Pin=J?-35
Added Pin To Net: NetName=Net2-40_18 Pin=J?-36
Added Pin To Net: NetName=Net1-39_2 Pin=J?-37
Added Pin To Net: NetName=Net2-40_19 Pin=J?-38
Added Pin To Net: NetName=Net1-39_1 Pin=J?-39
Added Pin To Net: NetName=Net2-40_20 Pin=J?-40
Added Pin To Net: NetName=Net41-59_10 Pin=J?-41
Added Pin To Net: NetName=Net42-60_1 Pin=J?-42
Added Pin To Net: NetName=Net41-59_9 Pin=J?-43
Added Pin To Net: NetName=Net42-60_2 Pin=J?-44
Added Pin To Net: NetName=Net41-59_8 Pin=J?-45
Added Pin To Net: NetName=Net42-60_3 Pin=J?-46
Added Pin To Net: NetName=Net41-59_7 Pin=J?-47
Added Pin To Net: NetName=Net42-60_4 Pin=J?-48
Added Pin To Net: NetName=Net41-59_6 Pin=J?-49
Added Pin To Net: NetName=Net42-60_5 Pin=J?-50
Added Pin To Net: NetName=Net41-59_5 Pin=J?-51
Added Pin To Net: NetName=Net42-60_6 Pin=J?-52
Added Pin To Net: NetName=Net41-59_4 Pin=J?-53
Added Pin To Net: NetName=Net42-60_7 Pin=J?-54
Added Pin To Net: NetName=Net41-59_3 Pin=J?-55
Added Pin To Net: NetName=Net42-60_8 Pin=J?-56
Added Pin To Net: NetName=Net41-59_2 Pin=J?-57
Added Pin To Net: NetName=Net42-60_9 Pin=J?-58
Added Pin To Net: NetName=Net41-59_1 Pin=J?-59
Added Pin To Net: NetName=Net42-60_10 Pin=J?-60
Added Pin To Net: NetName=Net61-79_10 Pin=J?-61
Added Pin To Net: NetName=Net62-80_1 Pin=J?-62
Added Pin To Net: NetName=Net61-79_9 Pin=J?-63
Added Pin To Net: NetName=Net62-80_2 Pin=J?-64
Added Pin To Net: NetName=Net61-79_8 Pin=J?-65
Added Pin To Net: NetName=Net62-80_3 Pin=J?-66
Added Pin To Net: NetName=Net61-79_7 Pin=J?-67
Added Pin To Net: NetName=Net62-80_4 Pin=J?-68
Added Pin To Net: NetName=Net61-79_6 Pin=J?-69
Added Pin To Net: NetName=Net62-80_5 Pin=J?-70
Added Pin To Net: NetName=Net61-79_5 Pin=J?-71
Added Pin To Net: NetName=Net62-80_6 Pin=J?-72
Added Pin To Net: NetName=Net61-79_4 Pin=J?-73
Added Pin To Net: NetName=Net62-80_7 Pin=J?-74
Added Pin To Net: NetName=Net61-79_3 Pin=J?-75
Added Pin To Net: NetName=Net62-80_8 Pin=J?-76
Added Pin To Net: NetName=Net61-79_2 Pin=J?-77
Added Pin To Net: NetName=Net62-80_9 Pin=J?-78
Added Pin To Net: NetName=Net61-79_1 Pin=J?-79
Added Pin To Net: NetName=Net62-80_10 Pin=J?-80
Added Pin To Net: NetName=Net81-119_20 Pin=J?-81
Added Pin To Net: NetName=Net82-120_1 Pin=J?-82
Added Pin To Net: NetName=Net81-119_19 Pin=J?-83
Added Pin To Net: NetName=Net82-120_2 Pin=J?-84
Added Pin To Net: NetName=Net81-119_18 Pin=J?-85
Added Pin To Net: NetName=Net82-120_3 Pin=J?-86
Added Pin To Net: NetName=Net81-119_17 Pin=J?-87
Added Pin To Net: NetName=Net82-120_4 Pin=J?-88
Added Pin To Net: NetName=Net81-119_16 Pin=J?-89
Added Pin To Net: NetName=Net82-120_5 Pin=J?-90
Added Pin To Net: NetName=Net81-119_15 Pin=J?-91
Added Pin To Net: NetName=Net82-120_6 Pin=J?-92
Added Pin To Net: NetName=Net81-119_14 Pin=J?-93
Added Pin To Net: NetName=Net82-120_7 Pin=J?-94
Added Pin To Net: NetName=Net81-119_13 Pin=J?-95
Added Pin To Net: NetName=Net82-120_8 Pin=J?-96
Added Pin To Net: NetName=Net81-119_12 Pin=J?-97
Added Pin To Net: NetName=Net82-120_9 Pin=J?-98
Added Pin To Net: NetName=Net81-119_11 Pin=J?-99
Added Pin To Net: NetName=Net82-120_10 Pin=J?-100
Added Pin To Net: NetName=Net81-119_10 Pin=J?-101
Added Pin To Net: NetName=Net82-120_11 Pin=J?-102
Added Pin To Net: NetName=Net81-119_9 Pin=J?-103
Added Pin To Net: NetName=Net82-120_12 Pin=J?-104
Added Pin To Net: NetName=Net81-119_8 Pin=J?-105
Added Pin To Net: NetName=Net82-120_13 Pin=J?-106
Added Pin To Net: NetName=Net81-119_7 Pin=J?-107
Added Pin To Net: NetName=Net82-120_14 Pin=J?-108
Added Pin To Net: NetName=Net81-119_6 Pin=J?-109
Added Pin To Net: NetName=Net82-120_15 Pin=J?-110
Added Pin To Net: NetName=Net81-119_5 Pin=J?-111
Added Pin To Net: NetName=Net82-120_16 Pin=J?-112
Added Pin To Net: NetName=Net81-119_4 Pin=J?-113
Added Pin To Net: NetName=Net82-120_17 Pin=J?-114
Added Pin To Net: NetName=Net81-119_3 Pin=J?-115
Added Pin To Net: NetName=Net82-120_18 Pin=J?-116
Added Pin To Net: NetName=Net81-119_2 Pin=J?-117
Added Pin To Net: NetName=Net82-120_19 Pin=J?-118
Added Pin To Net: NetName=Net81-119_1 Pin=J?-119
Added Pin To Net: NetName=Net82-120_20 Pin=J?-120
