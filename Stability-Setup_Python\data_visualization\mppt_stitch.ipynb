{"cells": [{"cell_type": "code", "execution_count": null, "id": "f78a4b80", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "id": "69951fd1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["107977.70135000002\n", "(2024, 18) (12243, 18) (14275, 18)\n", "[['Starting Voltage (V)' '0.50' 'None' ... 'None' 'None' 'None']\n", " ['<PERSON> Size (V)' '0.001' 'None' ... 'None' 'None' 'None']\n", " ['Cell Area (mm^2)' '0.128' 'None' ... 'None' 'None' 'None']\n", " ...\n", " ['761222.079475' '1.0100000000000002' '-0.8584999999999999' ...\n", "  '0.5199999999999998' '0.010000000000000002' '**********.0']\n", " ['761275.4701' '1.0100000000000002' '-0.905' ... '0.5199999999999998'\n", "  '0.010000000000000002' '**********.0']\n", " ['761328.85135' '1.0100000000000002' '-0.9615' ... '0.5199999999999998'\n", "  '0.010000000000000002' '**********.0']]\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import numpy_indexed as npi\n", "from labellines import labelLines\n", "from matplotlib.font_manager import FontProperties\n", "import os\n", "import sys\n", "import re\n", "\n", "def mppt_stitch(ss_beginning, ss_end, plot_size = (12,10)):\n", "\tarr = np.loadtxt(ss_beginning, delimiter=\",\", dtype=str)\n", "\theader_row = np.where(arr == \"Time\")[0][0]\n", "\n", "\theaders = arr[header_row, :]\n", "\tmeta_data = arr[:header_row + 1, :]\n", "\tarr = arr[header_row + 1 :, :]\n", "\n", "\theader_dict = {value: index for index, value in enumerate(headers)}\n", "\tmax_time_arr_1 = arr[-1, header_dict[\"Time\"]].astype(\"float\")\n", "\tprint(max_time_arr_1)\n", "\n", "\n", "\tarr2 = np.loadtxt(ss_end, delimiter=\",\", dtype=str)\n", "\theader_row = np.where(arr2 == \"Time\")[0][0]\n", "\n", "\theaders = arr2[header_row, :]\n", "\tmeta_data = arr2[:header_row + 1, :]\n", "\tarr2 = arr2[header_row + 1 :, :]\n", "\n", "\n", "\theader_dict = {value: index for index, value in enumerate(headers)}\n", "\ttime2 = np.array(arr2[:, header_dict[\"Time\"]]).astype(\"float\")\n", "\ttime2 += max_time_arr_1\n", "\ttime2 = time2.astype(\"str\")\n", "\n", "\tarr2[:, header_dict[\"Time\"]] = time2\n", "\n", "\n", "\n", "\tconcat_arr = np.concatenate((meta_data, arr, arr2), axis=0)\n", "\tprint(arr.shape, arr2.shape, concat_arr.shape)\n", "\n", "\tprint(concat_arr)\n", "\n", "\tnp.savetxt(r\"Apr-15-2025_14-56-30__concatted__ID3__compressedmppt.csv\",\n", "            concat_arr, delimiter=',', fmt='%s')\n", "\n", "\n", "\n", "\n", "\n", "mppt_stitch(r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\longmppt stitch april 15 - april 24\\Apr-15-2025 14_45_35\\Apr-15-2025 14_45_35\\Apr-15-2025_14-56-30__ID3__compressedmppt.csv\",\n", "            r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\longmppt stitch april 15 - april 24\\Apr-17-2025 01_30_43 (2)\\Apr-17-2025 01_30_43\\Apr-17-2025_01-31-43__ID3__compressedmppt.csv\")"]}, {"cell_type": "code", "execution_count": 17, "id": "aafeb68a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Apr-11-2025_22-34-49', 'ID4', 'mppt.csv']\n", "mppt\n", "ID4\n", "Apr-11-2025_22-34-49 mppt\n", "{'Apr-11-2025_22-34-49 mppt': ['C:/Users/<USER>/Dropbox/code/Stability-Setup/data/April-15-2025 11_40_35 concatted\\\\Apr-11-2025_22-34-49__ID4__mppt.csv']}\n"]}], "source": ["file = r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\April-15-2025 11_40_35 concatted\\Apr-11-2025_22-34-49__ID4__mppt.csv\"\n", "file = r\"C:/Users/<USER>/Dropbox/code/Stability-Setup/data/April-15-2025 11_40_35 concatted\\Apr-11-2025_22-34-49__ID4__mppt.csv\"\n", "head, tail = os.path.split(file)\n", "file_groups_dict = {}\n", "\n", "params = tail.split(\"__\")\n", "\n", "print(params)\n", "# Assume the last part before the extension indicates the file type\n", "filetype = params[-1].split(\".\")[0]\n", "print(filetype)\n", "# If \"ID\" is not present, use the second parameter as test_name\n", "test_name = params[1] if \"ID\" not in params[1] else \"\"\n", "print(params[1])\n", "name_parts = [val for val in [test_name, params[0], filetype] if val]\n", "plot_name = \" \".join(name_parts)\n", "print(plot_name)\n", "file_groups_dict.setdefault(plot_name, []).append(file)\n", "print(file_groups_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "cc975b39", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "stabilitySetup", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}