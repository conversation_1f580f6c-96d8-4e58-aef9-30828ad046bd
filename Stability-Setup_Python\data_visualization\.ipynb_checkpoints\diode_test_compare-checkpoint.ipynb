{"cells": [{"cell_type": "code", "execution_count": 2, "id": "8ba8d03d", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import numpy_indexed as npi\n", "from labellines import labelLines\n", "import os\n", "import sys\n"]}, {"cell_type": "code", "execution_count": 3, "id": "27082969", "metadata": {}, "outputs": [], "source": ["def get_stats(voltage, current):\n", "    V = np.asarray(voltage)\n", "    I = np.asarray(current)\n", "    if V.shape != I.shape:\n", "        raise ValueError(\"voltages and currents must have the same shape\")\n", "\n", "    # 1) Voc: interpolate V at I=0\n", "    #    Sort by current for reliable interpolation\n", "    sort_I = np.argsort(I)\n", "    I_sorted = I[sort_I]\n", "    V_sorted_by_I = V[sort_I]\n", "    Voc = float(np.interp(0.0, I_sorted, V_sorted_by_I))\n", "\n", "    # 2) Isc: interpolate I at V=0\n", "    sort_V = np.argsort(V)\n", "    V_sorted = V[sort_V]\n", "    I_sorted_by_V = I[sort_V]\n", "    Isc = float(np.interp(0.0, V_sorted, I_sorted_by_V))\n", "\n", "    # 3) Maximum power point\n", "    P = V * I\n", "    idx_mp = np.argmax(P)\n", "    Vmp = float(V[idx_mp])\n", "    Imp = float(I[idx_mp])\n", "\n", "    # 4) Fill Factor\n", "    FF = (Vmp * Imp) / (Voc * Isc) if (Voc * Isc) != 0 else np.nan\n", "\n", "    PCE = (Vmp * Imp) / 100 * 100\n", "\n", "    return FF, <PERSON>c, <PERSON><PERSON>, PCE"]}, {"cell_type": "code", "execution_count": 5, "id": "8d58253b", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "unterminated string literal (detected at line 137) (2128635928.py, line 137)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[5], line 137\u001b[1;36m\u001b[0m\n\u001b[1;33m    print(f\"Differences - FF: {ff_diff:.6f}, Isc: {isc_diff:.6f}, Voc: {voc_diff:.6f}, PCE: {pce_diff:.6F})\u001b[0m\n\u001b[1;37m          ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m unterminated string literal (detected at line 137)\n"]}], "source": ["\n", "\n", "litos_diode = rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\_Old\\photodiodetest\\Litos_analyzed\\100\\Data\\photo - 100p_1.0Sun_Scan0.txt\"\n", "sms_diode = rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-30-2025 16_26_04 Diode Test\\Apr-30-2025_16-26-10__ID1__light__scan.csv\"\n", "header = np.genfromtxt(\n", "    litos_diode,\n", "    delimiter=',',\n", "    dtype=str,\n", "    max_rows=1,\n", "    encoding='cp1252'\n", ")\n", "\n", "litos_data = np.genfromtxt(\n", "                    litos_diode,\n", "                    delimiter=',',\n", "                    comments='#',\n", "                    skip_header=1,\n", "                    encoding='cp1252'  # Specify the correct encoding\n", "                )\n", "\n", "arr = np.loadtxt(sms_diode, delimiter=\",\", dtype=str)\n", "header_row = np.where(arr == \"Time\")[0][0]\n", "\n", "litos_v = litos_data[:, ::2].astype(float)\n", "litos_ma = litos_data[:, 1::2].astype(float)*0.128\n", "\n", "meta_data = {}\n", "for data in arr[:header_row, :2]:\n", "    meta_data[data[0]] = data[1]\n", "\n", "headers = arr[header_row, :]\n", "arr = arr[header_row + 1 :, :]\n", "\n", "data = arr[:, 2:-1]\n", "\n", "sms_V = data[:, ::2].astype(float)\n", "top_V, bottom_V = np.split(sms_V, 2, axis=0)   # two arrays\n", "\n", "sms_mA = data[:, 1::2].astype(float)\n", "top_ma, bottom_ma = np.split(sms_mA, 2, axis=0)   # two arrays\n", "\n", "colors = plt.rcParams['axes.prop_cycle'].by_key()['color']\n", "\n", "\n", "custom_ylimits = {\n", "    # Add your custom limits here\n", "    # Example: 0: {'top': 25, 'bottom': -35},\n", "    # Example: 1: {'top': 15, 'bottom': -20},\n", "    0: {'top': 0.05, 'bottom': -0.05},\n", "    1: {'top': 0.05, 'bottom': -0.05},\n", "    2: {'top': 0.05, 'bottom': -0.05},\n", "    3: {'top': 0.05, 'bottom': -0.05},\n", "    4: {'top': 0.05, 'bottom': -0.05},\n", "    5: {'top': 0.05, 'bottom': -0.05},\n", "    6: {'top': 0.05, 'bottom': -0.15},\n", "    7: {'top': 0.05, 'bottom': -0.15},\n", "    8: {'top': 0.5, 'bottom': -3},\n", "    9: {'top': 0.5, 'bottom': -3},\n", "    10: {'top': 0.5, 'bottom': -14},\n", "    11: {'top': 0.5, 'bottom': -14},\n", "    12: {'top': 0.5, 'bottom': -20},\n", "    13: {'top': 0.5, 'bottom': -20},\n", "    14: {'top': 3, 'bottom': -15},\n", "    15: {'top': 3, 'bottom': -15},\n", "}\n", "\n", "# Helper function to set limits for multiple plots at once\n", "def set_limits_for_plots(plot_indices, top=None, bottom=None):\n", "    \"\"\"Set y-limits for multiple plots at once\n", "\n", "    Args:\n", "        plot_indices: list of plot indices (0-15)\n", "        top: top y-limit (optional)\n", "        bottom: bottom y-limit (optional)\n", "    \"\"\"\n", "    for i in plot_indices:\n", "        if i not in custom_ylimits:\n", "            custom_ylimits[i] = {}\n", "        if top is not None:\n", "            custom_ylimits[i]['top'] = top\n", "        if bottom is not None:\n", "            custom_ylimits[i]['bottom'] = bottom\n", "\n", "# Example usage of helper function:\n", "# set_limits_for_plots([0, 2, 4, 6, 8, 10, 12, 14], top=25, bottom=-35)  # Reverse plots\n", "# set_limits_for_plots([1, 3, 5, 7, 9, 11, 13, 15], top=20, bottom=-30)  # Forward plots\n", "# set_limits_for_plots([0, 1], top=30)  # Set only top limit for plots 0 and 1\n", "\n", "\n", "for i in range(16):\n", "    plt.figure(figsize=(12,5))\n", "    plt.xlim(0,1.2)\n", "\n", "    # Check if custom limits are defined for this plot\n", "    if i in custom_ylimits:\n", "        top = custom_ylimits[i].get('top')\n", "        bottom = custom_ylimits[i].get('bottom')\n", "    else:\n", "        # Use original logic as fallback\n", "        # top = np.max(litos_ma[:,i])*1.25\n", "\n", "        top = np.max(litos_ma[:,i])*1.25\n", "        bottom = np.min(litos_ma[:,i])*0.95\n", "\n", "    plt.ylim(bottom = bottom, top = top)\n", "    plt.xlabel('Voltage (V)')\n", "    plt.ylabel('Current (mA)')\n", "    plt.grid()\n", "    ax = plt.gca()\n", "    lineName = \"CAS\"\n", "    plt.plot(litos_v[:,i],\n", "                litos_ma[:,i],\n", "                linestyle='--',\n", "                color=colors[i % len(colors)],\n", "                label = f\"Litos{i//2+1}\")\n", "    if i%2 == 0:\n", "        plt.title(f\"Pixel{i} Reverse\")\n", "        plt.plot(bottom_V[:,i//2],\n", "                    bottom_ma[:,i//2],\n", "                    color=colors[i % len(colors)],\n", "                    label = f\"SMS{i//2+1}\")\n", "    else:\n", "        plt.plot(top_V[:,i//2],\n", "                    top_ma[:,i//2],\n", "                    color=colors[i% len(colors)],\n", "                    label = f\"SMS{i//2+1}\")\n", "        plt.title(f\"Pixel{i} Forward\")\n", "    if i == 14:\n", "        litos_stats = get_stats(litos_v[:,i], litos_ma[:,i])\n", "        sms_stats = get_stats(bottom_V[:,i//2], bottom_ma[:,i//2])\n", "        print(f\"litos {litos_stats}\")\n", "        print(f\"cas {sms_stats}\")\n", "        # Calculate absolute differences (FF, Isc, Voc)\n", "        ff_diff = abs(litos_stats[0] - sms_stats[0])\n", "        isc_diff = abs(litos_stats[1] - sms_stats[1])\n", "        voc_diff = abs(litos_stats[2] - sms_stats[2])\n", "        pce_diff = abs(litos_stats[3] - sms_stats[3])\n", "        \n", "        print(f\"Differences - FF: {ff_diff:.6f}, Isc: {isc_diff:.6f}, Voc: {voc_diff:.6f}, PCE: {pce_diff:.6F}\")\n", "        print(f\"Percent error ff {ff_diff/litos_stats[0]}\")\n", "        print(f\"Percent error isc {isc_diff/litos_stats[1]}\")\n", "        print(f\"Percent error voc {voc_diff/litos_stats[2]}\")\n", "        print(f\"Percent error PCE {pce_diff/litos_stats[3]}\")\n", "    elif i == 15:\n", "        litos_stats = get_stats(litos_v[:,i], litos_ma[:,i])\n", "        sms_stats = get_stats(top_V[:,i//2], top_ma[:,i//2])\n", "        print(f\"litos {litos_stats}\")\n", "        print(f\"cas {sms_stats}\")\n", "        # Calculate absolute differences (FF, Isc, Voc)\n", "        ff_diff = abs(litos_stats[0] - sms_stats[0])\n", "        isc_diff = abs(litos_stats[1] - sms_stats[1])\n", "        voc_diff = abs(litos_stats[2] - sms_stats[2])\n", "        pce_diff = abs(litos_stats[3] - sms_stats[3])\n", "        \n", "        print(f\"Differences - FF: {ff_diff:.6f}, Isc: {isc_diff:.6f}, Voc: {voc_diff:.6f}, PCE: {pce_diff:.6F}\")\n", "        print(f\"Percent error ff{ff_diff/litos_stats[0]}\")\n", "        print(f\"Percent error isc {isc_diff/litos_stats[1]}\")\n", "        print(f\"Percent error voc {voc_diff/litos_stats[2]}\")\n", "        print(f\"Percent error PCE {pce_diff/litos_stats[3]}\")\n", "        \n", "\n", "    else:\n", "        # Calculate and compare average slopes between litos and cas\n", "        if i % 2 == 0:  # Even indices (reverse bias)\n", "            # Calculate slope for litos\n", "            litos_slope = np.polyfit(litos_v[:,i], litos_ma[:,i], 1)[0]\n", "            # Calculate slope for cas (bottom)\n", "            cas_slope = np.polyfit(bottom_V[:,i//2], bottom_ma[:,i//2], 1)[0]\n", "            print(f\"Pixel{i} Reverse - Litos slope: {litos_slope:.6f}, CAS slope: {cas_slope:.6f}\")\n", "            print(f\"Absolute Difference: {abs(litos_slope - cas_slope):.6f}\")\n", "        else:  # Odd indices (forward bias)\n", "            # Calculate slope for litos\n", "            litos_slope = np.polyfit(litos_v[:,i], litos_ma[:,i], 1)[0]\n", "            # Calculate slope for cas (top)\n", "            cas_slope = np.polyfit(top_V[:,i//2], top_ma[:,i//2], 1)[0]\n", "            print(f\"Pixel{i} Forward - Litos slope: {litos_slope:.6f}, CAS slope: {cas_slope:.6f}\")\n", "            print(f\"Absolute Difference: {abs(litos_slope - cas_slope):.6f}\")\n", "\n", "\n", "    # plt.legend(bbox_to_anchor=(0.25, 0.16))\n", "    plt.legend()\n", "    plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}