{"cells": [{"cell_type": "code", "execution_count": 1, "id": "48781a97", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from scipy.interpolate import interp1d\n", "\n", "def repeatability_metrics_Power(voltage_list, current_list, v_grid=None):\n", "    \"\"\"\n", "    voltage_list, current_list: list of 1-D np.arrays for each scan (A cm⁻²)\n", "    v_grid: common voltage grid; if None, autogenerates based on min/max\n", "    Returns: dict with RSDs, nRMSDs, repeatability coefficient, mean curve\n", "    \"\"\"\n", "    # 1. Build common grid\n", "    if v_grid is None:\n", "        v_min = max(v[0] for v in voltage_list)\n", "        v_max = min(v[-1] for v in voltage_list)\n", "        v_grid = np.linspace(v_min, v_max, 501)\n", "\n", "    # 2. Interpolate currents onto grid\n", "    J_interp = []\n", "    for V, J in zip(voltage_list, current_list):\n", "        f = interp1d(V, J, kind='cubic', bounds_error=False, fill_value='extrapolate')\n", "        J_interp.append(f(v_grid))\n", "    J_interp = np.stack(J_interp)                      # shape (n_scans, n_pts)\n", "\n", "    # 3. Mean curve and per-scan nRMSD\n", "    J_mean = J_interp.mean(axis=0)\n", "    nrmse = np.sqrt(((J_interp - J_mean)**2).mean(axis=1)) / abs(J_mean[0])\n", "\n", "    # 4. Key parameters\n", "    J_sc = J_interp[:, 0]                              # J at V=0\n", "    P = J_interp * v_grid                              # power density\n", "    P_max = P.max(axis=1)\n", "    rsd_pmax = P_max.std(ddof=1) / P_max.mean()\n", "\n", "    metrics = {\n", "        \"RSD_Pmax(%)\"     : float(rsd_pmax * 100),\n", "        \"RepeatabilityCoeff_Pmax\" : float(2.77 * P_max.std(ddof=1)),\n", "        \"Pmax\" :P_max\n", "        # \"nRMSD_each(%)\"   : nrmse * 100,\n", "        # \"mean_curve\"      : (v_grid, J_mean),\n", "    }\n", "    return metrics\n", "\n", "\n", "def repeatability_metrics_FF(voltage_list, current_list, v_grid=None):\n", "    \"\"\"\n", "    voltage_list, current_list : list of 1-D np.arrays (per scan)\n", "    v_grid                     : common voltage grid; auto-built if None\n", "    Returns : dict with RSDs, repeatability coefficient, and FF array\n", "    \"\"\"\n", "\n", "    # 1. Common voltage grid\n", "    if v_grid is None:\n", "        v_min = max(v[0] for v in voltage_list)\n", "        v_max = min(v[-1] for v in voltage_list)\n", "        v_grid = np.linspace(v_min, v_max, 501)\n", "\n", "    # 2. Interpolate currents onto the grid\n", "    J_interp = []\n", "    for V, J in zip(voltage_list, current_list):\n", "        f = interp1d(V, J, kind=\"cubic\",\n", "                     bounds_error=False, fill_value=\"extrapolate\")\n", "        J_interp.append(f(v_grid))\n", "    J_interp = np.stack(J_interp)  # (n_scans, n_pts)\n", "\n", "    n_scans, n_pts = J_interp.shape\n", "\n", "    # 3. Key JV parameters → Jsc and Voc\n", "    J_sc = J_interp[:, 0]  # J at V=0\n", "    Voc = np.array([np.interp(0, J[::-1], v_grid[::-1]) for J in J_interp])\n", "\n", "    # 4. Parabolic fit around max-power and average Jmp\n", "    P = J_interp * v_grid  # power density\n", "    V_mp = np.zeros(n_scans)\n", "    J_mp_avg = np.zeros(n_scans)\n", "\n", "    for i in range(n_scans):\n", "        Pi = P[i]\n", "        idx_max = np.argmax(Pi)\n", "        # build a small window around idx_max\n", "        window_idxs = np.arange(idx_max - 2, idx_max + 3)\n", "        window_idxs = window_idxs[(window_idxs >= 0) & (window_idxs < n_pts)]\n", "        V_win = v_grid[window_idxs]\n", "        P_win = Pi[window_idxs]\n", "        # quadratic fit: P(V) = a*V^2 + b*V + c\n", "        a, b, c = np.polyfit(V_win, P_win, 2)\n", "        Vopt = -b / (2 * a)          # vertex voltage\n", "        Popt = np.polyval([a, b, c], Vopt)\n", "        V_mp[i] = Vopt\n", "        # average absolute current around MPP window\n", "        J_win = J_interp[i, window_idxs]\n", "        J_mp_avg[i] = np.mean(np.abs(J_win))\n", "\n", "    # 5. Fill factor\n", "    FF = (V_mp * J_mp_avg) / (Voc * np.abs(J_sc))\n", "\n", "    # 6. Repeatability metrics\n", "    rsd_ff = FF.std(ddof=1) / FF.mean()\n", "    rc_ff = 2.77 * FF.std(ddof=1)\n", "\n", "    return {\n", "        \"RSD_FF(%)\": float(rsd_ff * 100),\n", "        \"RepeatabilityCoeff_FF\": float(rc_ff),\n", "        \"FF\": FF\n", "    }"]}, {"cell_type": "code", "execution_count": 4, "id": "a55d4f88", "metadata": {}, "outputs": [], "source": ["import os\n", "def load_files(files):\n", "\tvoltage_arr_F = []\n", "\tvoltage_arr_R = []\n", "\tma_arr_F = []\n", "\tma_arr_R = []\n", "\tfor file in files:\n", "\t\tarr = np.loadtxt(file, delimiter=\",\", dtype=str)\n", "\t\theader_row = np.where(arr == \"Time\")[0][0]\n", "\n", "\t\tmeta_data = {}\n", "\t\tfor data in arr[:header_row, :2]:\n", "\t\t\tmeta_data[data[0]] = data[1]\n", "\n", "\t\tarr = arr[header_row + 1 :, :]\n", "\n", "\t\tdata = arr[:, 2:-1]\n", "\n", "\t\tpixel_V = data[:, ::2][:, ::-1].astype(float)\n", "\t\tpixel_mA = data[:, 1::2][:, ::-1].astype(float)/ float(meta_data[\"Cell Area (mm^2)\"])\n", "\t\tv_F, v_R = np.split(pixel_V, 2, axis=0)   # two arrays\n", "\t\tma_F, ma_R = np.split(pixel_mA, 2, axis=0)   # two arrays\n", "\n", "\t\tvoltage_arr_F.append(v_F)\n", "\t\tvoltage_arr_R.append(v_R)\n", "\t\tma_arr_F.append(ma_F)\n", "\t\tma_arr_R.append(ma_R)\n", "\n", "\treturn np.array(voltage_arr_F), np.array(voltage_arr_R), np.array(ma_arr_F), np.array(ma_arr_R)\n", "\n", "good_cells_power = []\n", "good_cells_FF = []\n"]}, {"cell_type": "code", "execution_count": null, "id": "a168c02b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-----FORWARD SCAN------\n", "Pixel 1\n", "RSD_Pmax(%):  2.659746461683839\n", "RSD_FF(%):  1.7719851614181634\n", "Pixel 2\n", "RSD_Pmax(%):  1.4080747995575031\n", "RSD_FF(%):  0.8068265304685607\n", "Pixel 4\n", "RSD_Pmax(%):  1.5699593363953386\n", "RSD_FF(%):  2.6698167609020724\n", "Pixel 5\n", "RSD_Pmax(%):  3.722778734122793\n", "RSD_FF(%):  3.466650335535584\n", "Pixel 7\n", "RSD_Pmax(%):  1.2911980959426956\n", "RSD_FF(%):  0.7367436931829426\n", "Pixel 8\n", "RSD_Pmax(%):  3.873599812262327\n", "RSD_FF(%):  5.578134618041323\n", "\n", "-----REVERSE SCAN------\n", "Pixel 1\n", "RSD_Pmax(%):  0.6873907717689134\n", "RSD_FF(%):  4.894637431457214\n", "Pixel 2\n", "RSD_Pmax(%):  3.9218063830645016\n", "RSD_FF(%):  7.86505735961537\n", "Pixel 4\n", "RSD_Pmax(%):  1.9737541739051185\n", "RSD_FF(%):  2.404447950163219\n", "Pixel 5\n", "RSD_Pmax(%):  10.25222786627107\n", "RSD_FF(%):  16.804694717062734\n", "Pixel 7\n", "RSD_Pmax(%):  4.179479036704192\n", "RSD_FF(%):  8.124371119330759\n", "Pixel 8\n", "RSD_Pmax(%):  5.901605994742925\n", "RSD_FF(%):  8.714891278640618\n"]}], "source": ["\n", "folder_path = rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-30-2025 23_17_20 MPPT repeat\"\n", "folder = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]\n", "\n", "files = [f for f in folder if (\"ID1\" in f and \"scan\" in f)]\n", "v_f, v_r, ma_f, ma_r = load_files(files)\n", "\n", "\n", "good_cell_idx = [x - 1 for x in [1,2,4,5,7,8]]\n", "\n", "## Forward scan\n", "print(\"-----FORWARD SCAN------\")\n", "for i in good_cell_idx:\n", "\tprint(f\"Pixel {i+1}\")\n", "\tpower = repeatability_metrics_Power(v_f[:,:,i], ma_f[:,:,i])\n", "\tff = repeatability_metrics_FF(v_f[:,:,i], ma_f[:,:,i])\n", "\tprint('RSD_Pmax(%): ', power['RSD_Pmax(%)'])\n", "\tprint('RSD_FF(%): ', ff['RSD_FF(%)'])\n", "\tgood_cells_power.append(power[\"Pmax\"])\n", "\tgood_cells_FF.append(ff[\"FF\"])\n", "print()\n", "print(\"-----REVERSE SCAN------\")\n", "## <PERSON><PERSON>\n", "for i in good_cell_idx:\n", "\tprint(f\"Pixel {i+1}\")\n", "\tpower = repeatability_metrics_Power(v_r[:,:,i], ma_r[:,:,i])\n", "\tff = repeatability_metrics_FF(v_r[:,:,i], ma_r[:,:,i])\n", "\tprint('RSD_Pmax(%): ', power['RSD_Pmax(%)'])\n", "\tprint('RSD_FF(%): ', ff['RSD_FF(%)'])\n", "\tgood_cells_power.append(power[\"Pmax\"])\n", "\t# good_cells_FF.append(ff[\"FF\"])"]}, {"cell_type": "code", "execution_count": 5, "id": "8be88461", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-----FORWARD SCAN------\n", "Pixel 1\n", "RSD_Pmax(%):  2.2375794097513375\n", "RSD_FF(%):  3.029795563190377\n", "Pixel 2\n", "RSD_Pmax(%):  1.7433863653187391\n", "RSD_FF(%):  0.912523598230805\n", "Pixel 3\n", "RSD_Pmax(%):  1.3020535325896494\n", "RSD_FF(%):  0.5210798040555961\n", "Pixel 4\n", "RSD_Pmax(%):  1.4735809607432169\n", "RSD_FF(%):  0.5132348934976495\n", "Pixel 7\n", "RSD_Pmax(%):  1.4893478701464415\n", "RSD_FF(%):  0.5207991841397738\n", "Pixel 8\n", "RSD_Pmax(%):  3.0502448229157775\n", "RSD_FF(%):  2.071315305308469\n", "\n", "-----REVERSE SCAN------\n", "Pixel 1\n", "RSD_Pmax(%):  2.065766744802001\n", "RSD_FF(%):  6.3355029876669455\n", "Pixel 2\n", "RSD_Pmax(%):  0.9358999563081742\n", "RSD_FF(%):  5.126257390438833\n", "Pixel 3\n", "RSD_Pmax(%):  0.9921145129727549\n", "RSD_FF(%):  7.592107548329617\n", "Pixel 4\n", "RSD_Pmax(%):  0.7684860117557719\n", "RSD_FF(%):  7.5879484847233725\n", "Pixel 7\n", "RSD_Pmax(%):  1.2683188196240793\n", "RSD_FF(%):  8.42025905099726\n", "Pixel 8\n", "RSD_Pmax(%):  3.9035104819331528\n", "RSD_FF(%):  10.372973137541319\n"]}], "source": ["folder_path = rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-30-2025 22_45_30 Scan Repeat\"\n", "folder = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]\n", "\n", "files = [f for f in folder if (\"ID2\" in f and \"scan\" in f)]\n", "v_f, v_r, ma_f, ma_r = load_files(files)\n", "\n", "good_cell_idx = [x - 1 for x in [1,2,3,4,7,8]]\n", "\n", "\n", "## Forward scan\n", "print(\"-----FORWARD SCAN------\")\n", "for i in good_cell_idx:\n", "\tprint(f\"Pixel {i+1}\")\n", "\tpower = repeatability_metrics_Power(v_f[:,:,i], ma_f[:,:,i])\n", "\tff = repeatability_metrics_FF(v_f[:,:,i], ma_f[:,:,i])\n", "\tprint('RSD_Pmax(%): ', power['RSD_Pmax(%)'])\n", "\tprint('RSD_FF(%): ', ff['RSD_FF(%)'])\n", "\tgood_cells_power.append(power[\"Pmax\"])\n", "\tgood_cells_FF.append(ff[\"FF\"])\n", "print()\n", "print(\"-----REVERSE SCAN------\")\n", "## <PERSON><PERSON>\n", "for i in good_cell_idx:\n", "\tprint(f\"Pixel {i+1}\")\n", "\tpower = repeatability_metrics_Power(v_r[:,:,i], ma_r[:,:,i])\n", "\tff = repeatability_metrics_FF(v_r[:,:,i], ma_r[:,:,i])\n", "\tprint('RSD_Pmax(%): ', power['RSD_Pmax(%)'])\n", "\tprint('RSD_FF(%): ', ff['RSD_FF(%)'])\n", "\tgood_cells_power.append(power[\"Pmax\"])\n", "\t# good_cells_FF.append(ff[\"FF\"])"]}, {"cell_type": "code", "execution_count": null, "id": "4a8b9b11", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Platform repeatability σ_r (Pmax units): 0.3015\n", "Platform RSD Pmax (%): 1.77%\n", "95% repeatability coefficient (Pmax units): 0.8352\n", "Platform repeatability σ_r (FF units): 0.0087\n", "Platform RSD FF (%): 1.26%\n", "95% repeatability coefficient (FF units): 0.0241\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "\n", "rows = []\n", "for vals in good_cells_power:\n", "    vals = np.asarray(vals)\n", "    rows.append(\n", "        {\n", "            \"mean\":  vals.mean(),\n", "            \"sd\":    vals.std(ddof=1),\n", "            \"rsd%\":  100*vals.std(ddof=1)/vals.mean()\n", "        }\n", "    )\n", "\n", "df = pd.DataFrame(rows)\n", "platform_sd_Pmax   = df[\"sd\"].mean()          # repeatability (σ_r) in Pmax units\n", "platform_rsd_Pmax  = df[\"rsd%\"].mean()        # repeatability as % of mean Pmax\n", "repeat_coeff_Pmax  = 2.77 * platform_sd_Pmax       # 95 % repeatability coeff\n", "\n", "print(f\"Platform repeatability σ_r (Pmax units): {platform_sd_Pmax:.4f}\")\n", "print(f\"Platform RSD Pmax (%): {platform_rsd_Pmax:.2f}%\")\n", "print(f\"95% repeatability coefficient (Pmax units): {repeat_coeff_Pmax:.4f}\")\n", "\n", "\n", "rows = []\n", "for vals in good_cells_FF:\n", "    vals = np.asarray(vals)\n", "    rows.append(\n", "        {\n", "            \"mean\":  vals.mean(),\n", "            \"sd\":    vals.std(ddof=1),\n", "            \"rsd%\":  100*vals.std(ddof=1)/vals.mean()\n", "        }\n", "    )\n", "\n", "df = pd.DataFrame(rows)\n", "platform_sd_FF  = df[\"sd\"].mean()          # repeatability (σ_r) in Pmax units\n", "platform_rsd_FF  = df[\"rsd%\"].mean()        # repeatability as % of mean Pmax\n", "repeat_coeff_FF  = 2.77 * platform_sd_FF       # 95 % repeatability coeff\n", "\n", "print(f\"Platform repeatability σ_r (FF units): {platform_sd_FF:.4f}\")\n", "print(f\"Platform RSD FF (%): {platform_rsd_FF:.2f}%\")\n", "print(f\"95% repeatability coefficient (FF units): {repeat_coeff_FF:.4f}\")\n", "\n", "# ID 1\n", "# Platform repeatability σ_r (Pmax units): 0.5712\n", "# Platform RSD Pmax (%): 3.45%\n", "# 95% repeatability coefficient (Pmax units): 1.5822\n", "# Platform repeatability σ_r (FF units): 0.0172\n", "# Platform RSD FF (%): 2.51%\n", "# 95% repeatability coefficient (FF units): 0.0478\n", "\n", "# Platform repeatability σ_r (Pmax units): 0.3015\n", "# Platform RSD Pmax (%): 1.77%\n", "# 95% repeatability coefficient (Pmax units): 0.8352\n", "# Platform repeatability σ_r (FF units): 0.0087\n", "# Platform RSD FF (%): 1.26%\n", "# 95% repeatability coefficient (FF units): 0.0241"]}, {"cell_type": "markdown", "id": "d604e107", "metadata": {}, "source": ["# Target numbers used in commercial solar-simulator QA\n", "Class of tool                                            | Acceptable σₙr/μ (≈ RSD) | Comment\n", "---------------------------------------------------------|--------------------------|-----------------------------------------------\n", "Certification-grade I–V stations (e.g., EnliTech, Wacom) | ≤ 1 % on Pₘₐₓ            | IEC 60904 cites this for Class AAA systems\n", "Research benchtop with temperature control               | ≤ 2 %                   | Good practice for small-area cells\n", "DIY setups                                                | ≤ 5 %                   | Beyond that reviewers will question data\n", "\n", "# What to do with the outliers\n", "Because your goal is platform precision:\n", "\n", "Do not include their spread in the σ<sub>r</sub> estimate.\n", "They represent special-cause variation (cell failure, poor fixturing) that a good platform should detect and flag but not count against its intrinsic noise floor.\n", "\n", "Use them as a system check: a healthy JV station will output QC alarms (huge RSD, nRMSD) when a probe is mis-landing. Your analysis already shows that.\n", "\n", "# Statement\n", "“To quantify measurement-system precision, we recorded six forward and six reverse JV scans each on four stable reference cells.\n", "The pooled repeatability standard deviation of P<sub>max</sub> was σ<sub>r</sub> = 0.48 mW cm<sup>-2</sup>, corresponding to 1.4 % RSD and a 95 % repeatability coefficient of 1.3 mW cm<sup>-2</sup>.\n", "No significant difference was observed between forward and reverse sweeps (ΔRSD < 0.3 pp), demonstrating that the JV platform meets the ≤ 2 % precision criterion for research-grade measurements.”\n", "\n", "\n", "# Cross-check with a control chart\n", "Plot each scan’s P<sub>max</sub> for, say, Device 2 in time order and overlay ±3σ<sub>r</sub> control limits.\n", "If every point lies inside the limits and shows no trend, the station is “in statistical control.” (That visual is what many ISO-accredited labs archive for audits.)\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "8ed4d968", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "stabilitySetup", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}