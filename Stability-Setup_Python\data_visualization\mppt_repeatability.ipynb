{"cells": [{"cell_type": "code", "execution_count": 1, "id": "77903fe2", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import os\n", "import pandas as pd\n", "def detect_settled_index(t, P, window, thresh):\n", "\tt = np.asarray(t, dtype=float)\n", "\tP = np.asarray(P, dtype=float)\n", "\n", "\t# --- rest of your function unchanged --------------------\n", "\tfor start in np.arange(t[0], t[-1] - window, window):\n", "\t\tend   = start + window\n", "\t\tmask  = (t >= start) & (t <= end)\n", "\t\tif mask.sum() < 2:\n", "\t\t\tcontinue\n", "\t\tsegment   = P[mask]\t\t  # now works: P is an ndarray\n", "\t\tmean_seg  = segment.mean()\n", "\t\tif mean_seg == 0:\n", "\t\t\tcontinue\n", "\t\tif np.abs(segment - mean_seg).max() < thresh * mean_seg:\n", "\t\t\treturn np.where(t >= start)[0][0]   # first index in settled window\n", "\treturn None\n", "def mppt_repeatability(time_list, power_list, settle_thresh=0.01, min_window=30.0, settle_window=5.0):\n", "\t\"\"\"\n", "\tCompute repeatability metrics for MPPT runs.\n", "\n", "\tParameters:\n", "\t- time_list: list of 1D numpy arrays of time stamps (seconds)\n", "\t- power_list: list of 1D numpy arrays of power readings (same shapes as time_list)\n", "\t- settle_thresh: relative std/mean threshold for detecting steady state\n", "\t- min_window: duration (s) of the steady-state window to analyze\n", "\t- settle_window: duration (s) for the rolling stability check\n", "\n", "\tReturns:\n", "\t- dict with:\n", "\t  * \"RSD_Power(%)\": pooled relative standard deviation (%) of mean powers\n", "\t  * \"RC_MPPT\": 95% repeatability coefficient (same units as power)\n", "\t  * \"means\": array of per-run mean powers\n", "\t  * \"stds\": array of per-run std deviations\n", "\t\"\"\"\n", "\twindows = []\n", "\tfor idx in range(len(time_list)):\n", "\t\tt = time_list[idx]\n", "\t\tP = power_list[idx]\n", "\t\t# detect start of steady-state\n", "\t\tidx0 = detect_settled_index(t, P, settle_window, settle_thresh)\n", "\t\tif idx0 is None:\n", "\t\t\t# fallback: use last min_window seconds\n", "\t\t\tstart_time = t[-1] - min_window\n", "\t\telse:\n", "\t\t\tstart_time = t[idx0]\n", "\t\tmask = (t >= start_time) & (t < start_time + min_window)\n", "\t\twindows.append(P[mask])\n", "\n", "\tmeans = np.array([w.mean() for w in windows])\n", "\tstds  = np.array([w.std(ddof=1) for w in windows])\n", "\n", "\t# platform metrics\n", "\tplatform_rsd = stds.std(ddof=1) / means.mean()\n", "\trc95\t\t = 2.77 * stds.std(ddof=1)\n", "\n", "\treturn {\n", "\t\t\"RSD_Power\": platform_rsd * 100,\n", "\t\t\"RepeatabilityCoeff_MPPT\": rc95,\n", "\t\t\"windows\": windows,\n", "\t\t\"means\": means,\n", "\t\t\"stds\": stds\n", "\t}\n"]}, {"cell_type": "code", "execution_count": 2, "id": "cf8ec2cf", "metadata": {}, "outputs": [], "source": ["def load_files(files):\n", "\ttime_all = []\n", "\tpower_all = []\n", "\n", "\tfor file in files:\n", "\t\tarr = np.loadtxt(file, delimiter=\",\", dtype=str)\n", "\t\theader_row = np.where(arr == \"Time\")[0][0]\n", "\n", "\t\tmeta_data = {}\n", "\t\tfor data in arr[:header_row, :2]:\n", "\t\t\tmeta_data[data[0]] = data[1]\n", "\n", "\t\theaders = arr[header_row, :]\n", "\t\tarr = arr[header_row + 1 :, :]\n", "\n", "\t\theader_dict = {value: index for index, value in enumerate(headers)}\n", "\t\ttime = np.array(arr[:, header_dict[\"Time\"]]).astype(\"float\")\n", "\n", "\t\tpixel_V = arr[:, 1::2][:, 0:8].astype(float)\n", "\t\tpixel_mA = arr[:, 2::2][:, 0:8].astype(float)\n", "\t\t# print(pixel_V.shape, pixel_mA.shape)\n", "\n", "\t\tpower = (pixel_V*pixel_mA).T\n", "\t\ttime_all.append(time)\n", "\t\tpower_all.append(power)\n", "\n", "\ttime_collated  = [[] for _ in range(8)]\n", "\tpower_collated = [[] for _ in range(8)]\n", "\tfor i in range(len(time_all)):\n", "\t\t# print(files[i])\n", "\t\tfor j in range(8):\n", "\t\t\ttime_collated[j].append(time_all[i])\n", "\t\t\tpower_collated[j].append(power_all[i][j])\n", "\n", "\treturn time_collated, power_collated\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "176d88f2", "metadata": {}, "outputs": [], "source": ["good_cells_RSD = []\n", "good_cells_std = []"]}, {"cell_type": "code", "execution_count": null, "id": "f40ae2f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RSD_Power(%): 1.231713029390777, RepeatabilityCoeff_MPPT: 0.07585190978410536\n", "RSD_Power(%): 0.9936694693885846, RepeatabilityCoeff_MPPT: 0.046040439729029374\n", "RSD_Power(%): 1.9744649835115786, RepeatabilityCoeff_MPPT: 0.00010082671764802579\n", "RSD_Power(%): 0.37525081985271175, RepeatabilityCoeff_MPPT: 0.019360614215699595\n", "RSD_Power(%): 0.5956268420275339, RepeatabilityCoeff_MPPT: 0.03738218561879932\n", "RSD_Power(%): 0.1713809089182657, RepeatabilityCoeff_MPPT: 0.011492677367515117\n", "RSD_Power(%): 1.5720339818249849, RepeatabilityCoeff_MPPT: 0.0972545819123626\n", "RSD_Power(%): 0.21269807556923898, RepeatabilityCoeff_MPPT: 0.014043295139227233\n"]}], "source": ["\n", "folder_path = rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-30-2025 23_17_20 MPPT repeat\"\n", "folder = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]\n", "\n", "files = [f for f in folder if (\"ID1\" in f and \"mppt\" in f and \"compressed\" not in f)]\n", "\n", "time_all, power_all = load_files(files)\n", "\n", "good_cell_idx = [x - 1 for x in [1,2,3,4,5,6,7,8]]\n", "for idx in good_cell_idx:\n", "\tresults = mppt_repeatability(time_all[idx], power_all[idx])\n", "\tprint(f\"RSD_Power(%): {results['RSD_Power']}, RepeatabilityCoeff_MPPT: {results['RepeatabilityCoeff_MPPT']}\")\n", "\tgood_cells_RSD.append(results['RSD_Power'])\n", "\tgood_cells_std.append(results['stds'])"]}, {"cell_type": "code", "execution_count": 4, "id": "5309d14c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RSD_Power(%): 0.3079958190271592, RepeatabilityCoeff_MPPT: 0.018361083135744156\n", "RSD_Power(%): 0.4949265782517376, RepeatabilityCoeff_MPPT: 0.02870707714623788\n", "RSD_Power(%): 0.08871252388706266, RepeatabilityCoeff_MPPT: 0.0030073117290785583\n", "RSD_Power(%): 0.343836355884467, RepeatabilityCoeff_MPPT: 0.021197109507703114\n", "RSD_Power(%): 0.06279214104911239, RepeatabilityCoeff_MPPT: 0.004018583963090676\n", "RSD_Power(%): 0.7161358928865493, RepeatabilityCoeff_MPPT: 0.04060241731704219\n"]}], "source": ["folder_path = rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-30-2025 23_17_20 MPPT repeat\"\n", "folder = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]\n", "\n", "files = [f for f in folder if (\"ID2\" in f and \"mppt\" in f and \"compressed\" not in f)]\n", "\n", "time_all, power_all = load_files(files)\n", "\n", "good_cell_idx = [x - 1 for x in [1,2,5,6,7,8]]\n", "for idx in good_cell_idx:\n", "\tresults = mppt_repeatability(time_all[idx], power_all[idx])\n", "\tprint(f\"RSD_Power(%): {results['RSD_Power']}, RepeatabilityCoeff_MPPT: {results['RepeatabilityCoeff_MPPT']}\")\n", "\tgood_cells_RSD.append(results['RSD_Power'])\n", "\tgood_cells_std.append(results['stds'])\n"]}, {"cell_type": "code", "execution_count": 5, "id": "67ed904e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Platform repeatability σ_r (mW): 0.0331\n", "Platform RSD power (%): 0.34%\n", "95% repeatability coefficient (mW): 0.0918\n"]}], "source": ["platform_sd_Pmax   = np.array(good_cells_std).mean()\t\t  # repeatability (σ_r) in Pmax units\n", "platform_rsd_Pmax  = np.array(good_cells_RSD).mean()\t\t# repeatability as % of mean Pmax\n", "repeat_coeff_Pmax  = 2.77 * platform_sd_Pmax\t   # 95 % repeatability coeff\n", "\n", "print(f\"Platform repeatability σ_r (mW): {platform_sd_Pmax:.4f}\")\n", "print(f\"Platform RSD power (%): {platform_rsd_Pmax:.2f}%\")\n", "print(f\"95% repeatability coefficient (mW): {repeat_coeff_Pmax:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "06823df1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "stabilitySetup", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}