{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAcYAAAHACAYAAAA4O8g0AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAArW9JREFUeJzsnXecFdXduJ9pt5ftfZddels6qICIiti7JkZjYqLG9Jje3vRmkveN75vE9KgpaqzRqFhRRBBBkN5h2cL2fvf2Kef3x9y9uwssgrEk+c3DZzgzs2fOnCl3vqd8iySEEDg4ODg4ODgAIL/bFXBwcHBwcPhXwhGMDg4ODg4Ow3AEo4ODg4ODwzAcwejg4ODg4DAMRzA6ODg4ODgMwxGMDg4ODg4Ow3AEo4ODg4ODwzAcwejg4ODg4DAM9d2uwNuNZVm0tLQQDAaRJOndro6Dg4ODw7uAEIKBgQHKysqQ5eP3Cf/jBWNLSwuVlZXvdjUcHBwcHP4FaGpqoqKi4rh5/uMFYzAYBOybEQqFjptX13WeffZZli9fjqZp70T1/q1w7s/xce7P8XHuz/Fx7s/x+WfvTyQSobKyMisTjsd/vGAcHD4NhUInJBh9Ph+hUMh5MY+Bc3+Oj3N/jo9zf46Pc3+Oz1t1f05kSs1RvnFwcHBwcBiGIxgdHBwcHByG4QhGBwcHBweHYTiC0cHBwcHBYRiOYHRwcHBwcBiGIxgdHBwcHByG4QhGBwcHBweHYTiC0cHBwcHBYRiOYHRwcHBwcBiGIxgdHBwcHByG4QhGBwcHBweHYTiC0cHBwcHBYRiOYHRwcHBwcBiGIxgdHBwcHByG8R8fdsrB4d1AGBZWwsBKGoikiZU0sJImIpVJB7d1EywQlgBLHJUOrgtDgGkhzKFUmAIMCzG4P4sEmcg6IyLsSIAAIbD/G5aK4duShCRLIEtIypGpbDenJWmoDEsMlWnZabY8SUIazC/BtESYrv1b7PKHnQeJ7D7kzDHD80hk9mfWj7Uf7PsgxFCdrCPWAUmThy3KEdsykipn7sUR9yV7nYP3aVg95CPqNPzaZIatD7u3g/sATAs9bRDq1Uju7kGXFLAGn3em3i4Fya0gZ1LJrSC7FXu/S0aSJPtdSZtYKRORst87O7W3Rcqw77GSeaaKjKRk6qLKdjr4N2mU5zJ8f+b4wXfEXrffkRMJ7/SviiMYHRyOgxCZD01Ux4zpWFEdK6ZjxQ2sRCaN26kZ16ntCdO+cQPo1rtddcD+hr+ZowYF7Zs7fnQ8KJjJ5Ftc6n8OEwjSv2ffyR8ogaTKiH+R9w6wBaws2Y0JRLaxkV2HoXSw8aXYjS9JyQjqYQJcznXDG8cYfktwBKPD/9cIU2D0JDDa4+jtcYzuBOag8IvqmLE0GCcuHlwo2N0m7I+VS0H2qEgeO5U9CtLwVJNBGdl7yrbAB1vqymBrXAZ12PrgR0Qd1vPIXtjwdTG0L9ObQcq06Ae35WHbwr4v2d7rYC91+PZgz1I6dm8pm2Z6Wgg70Oy6tes47dRTUWQl28MUg3kGe87De2eZcw3tH8zPUfuBY/c2M9uDPRihW5nFHLZuYWW2MayR15B5DINlMnSbstc2olcqjuitHtlrHX5vMz1Su4cGkdgAodwwsqIMCYrMs7XSFiKd6QlmUpE2h3q3w4WiLNnvmFtBdmfev0wvE7BHICyBMC27Pubwdevo5zLsOWbv++C7MLj/SExxxEjGcRi8TzoIzGNmUXXTEYwO7w7CMBCmiex2v7XlWtbQh/QtZOCFF0nt3UPgjDNwT5kyavnCsDC6E+gdcVsIdmQEYVciO1R13PqrEpZXRXS3QE8bIh1F6DGEngBNIHlUJL+LfafMYsn5Z+MO++j9+4Mkd+8h1R3HjMUxE0msRAIrlcIyTPp+9nuShkXKsMj53e0ENq5FSBKJghIiheVECkrpySulJ7+Mgbwie4gKUNNJAj0dBHo77DSz7kklwOVi12e/h8+l4NUUqv78S7x7t9sf2mAIEQxhBEKkfQFSviCtyy4lKSTiaZPA6+txtR1GSSfx6CncegqXnkLFQpEkem/9Gh6XikdT8D75MOzehZAkhKZhqhqW5sJUNCxNo+fCq7A0F4os4T1cjzYQAZ+XPd3t+Pq7cOfkomkyqmmh+DyoiowqS0g9XSiRCLKwUDQVxaOhujRQVFBVpNxcUBRbFvb1Y/b3YaXTiLSObOp4sFDdLuRQGK2iHNnlekvft7cTXddZv2IFF1yw+IQj1AshbMGesoW65JKR3ardgHoHhzJFVuAPE7SZfSMaGDD0nzS4ji0YjdEFtTAFlmTB7uZ35HocweiQRZgmdZddhhWPU/PQQ6h5ef90mWY0SvOtnyWxbRuy203NIw+jFha+BbWF7jvvouMnPwGg8/9+TnD5cvK++2N6GvsZaImS7khATxJXJI0/bo6qaWaYOlKkGWugBSvajpXsR6QGEKkBzHSUq5d9kZihwAB8fvsjLGvaRFx14zNSI8rRJYVbwvNh78u4FJmvrn2YU9t2HnU+CVCAD/5hHaZst+C/tOcwZ/b1AODu7SZn/8jjrr7gu0RdPgBuf+nnTO5tPOa1JBSNHz+/BknWQdL5zvbNzGttGpFHAdyAT5K4UWsGWYBk8o1nNlLb1DXq/f7gjB6QLSTJ4vPPNrDoQGTUvJ8ufoiU20IIjY89leDsHfbw6dkAv/npiLzvu6mWuEdCwuLjLzSxbFf/qOV+9OZcusISSBYfXhnngtfTo+b9wg35tBR5USSN8zZGOXNLhJRLxpIYtghMCX6/PJf2HBkLi9P2xDl9dwIhSVhSppMkSVgymLLEQ6eGaMtVkSSZaY0pTtlv57W/84P/7PWVc/JpzfNiCZmq1iS1hwZIKgopRSGpKCRlhaQmk3bLtBa6ibotvnf3fciygSyZoJggmUgIZNzIwoOEB8lyIQk3wsoshoZsqmgYuEQaDR3TJUh7QFJMJMl+H4RkICEjoSIJBQkVhAooSJkUoWbySMiS/auRJBlZylybBLI9TIAlJCwLLMteNzPrpgWmJSEAWRLIsoUkCWTJQpIzqSSQZAsJC1nSkNCQ0ZCFnUoM7nMhCxfFwSCL3iF1UUcwOmRJ7d9P+sBBANp/dBvlP/3JSR0/8OKLRF94ATkYovhLXwRA9vtJ7tyJFYlgAf2PPUb+TTcddaxlmsiKctzyDdOiLZLkcE8c89e/pnDdRrSxZ5EsmYLXFSLpLqPntteAY4+4CD2JNdBKKt7GCzlF7NA0GjCY2riRCZE2/JZBwNRxGzouPYWhqHQWljHel0KXZVQ9wfoZC1g/fQ5CGAhDp9E9Bl88RjgRoSTVyil99vlNSWF7aQkNeSHSsouU4iah5WJ6vKC50VSLWWEDl2riVqBuyTLazMW4MQkOdJMfHyCnp4VwZxu6kWL52C3oVoKUlCBSodNmeYkEZXoDgs6QTluOyYAXDNkkOO6/s9f8oEewIimjWOBPQjAJgQQEEwLVBHfJ09m8e8ZbJIMSSRckNUi6IKVJGJnHooW3ARKygDUzBAcrFSQBmikyC2gGuAwwPDEkWUIiSSRocjgffCl78egjn4vXtwvdb3cd4kGTPh9YMiiWvagmyBaoFgh3BFmz86bcJnEXGAroKuiKve4y7PNE/X0YSj8GEBowKesefWRAFR1YmXLL+i3mHRx9ru6ZeWnaXPZHv7rL4tzNo+ddM6mfWKH9Na/qtHjP2tHz/vRKmdcmyqSAxTssPv6kRVplqFfF0Ijlry6y86LAwl0Wtz527HJNCX57gcyqGXYdxrQLLl9nkVIzHTaRaRdlludnSeystvNWdgouXWdhySAGGxKyXaYpw4ZJMnuq7MrlDgiW7BBYwwXXsNu9p1Jif7md158QLNgnMGT7eRmKXZ6Q7Lq05km05Nt5vSnB7IOCVydLSL3FLMr/9Kj3763EEYwOWTyTJ1P5u9/S9JFbiDz+OOFLLiFw+uITOrbvH4+z6YffRbEsSj0Bir74BaTM0GnpD39AYts2un/9G/of+wd5N96YHeZp3rOL9Y8+wKEtm/D4/Fz13f+mXwtT1xll/+59tLZ3E0/6CPYlKYunGYtCteTBry2F05fa9R5WDyEEcSNCRO9hQO9mQO8hovcwd+vrGJogNvc09hYaxFpWUwPUAKjQnweD/ZSP/f4BfKEwAI//749Zsu5Po173H//4abyBIKYlePo3P2fPS8+NzDBsROzG//kDVthF80Azr91zL73rXjhmmTpwxxnNDPgNAObuyaH25a3Zv3fJPrqqfdntJ+e10h22v0KzDuTwwafCo9a39dIyImUBYpKCf0cfH3y+fYTqar9/6GOZc+lcXGOKUWUV/fUGbljx+qjlTvzQlZTOqkVTNHp3HuCT/3gGxaVhWRZmjs7+OTqmYZCKJ6i48kx8lfnoRpr++haufm4rkqogaypygcqW5SqypqK4XBSeNofwhLGosorZP8Dntu/H5fGgKCrKVQoD79VQZAVVUQlXVCJywkSSCZrbW/nAzl0kkylSqSRifD9PFQ2AaaK6XLiDQbx+P5ok45Jl3jdrGlpOGI+mESg6TNfMBizTRJgmQghkkZk7NAw+eNps9PwcTCTcngM0h3ZiWiaWEJhCYGGnphDMn1xLbVEIWYXC9CEaYztQdAMlnc6kOkpSR03pnDZhOZpkUl09gerGbajWU6ijdIhnBc/EX1yGIZLUNB8Ejv1sFAGVOadxas44hFCZ0NjEwt3PHTMvQO/kaRAqRQiLsW1dLNm5ddS8VkElycmlgKC6s5/rVu0eNe8z55QhJpUjSQplA3E+tmLb6HmXFPL4WbmYQqegL8qtj3Vy84QQgVDOqMe81TiC0WEEgSVLyPvgB+j5059p+/a3Gfv4P5B9vuMekz58mFf/76fsHFMMgM/np/Nvf6H27OWEi0oInnkmvjlz6PnDH0nt309yzx4ODURY8/D9DNQPaeAlY1Eu/8XrjJfcTEVhYXqAi9QQmuwG/HDEdFFcpOgvCiEKvfQd3khn2y6SDCAwEekUVjSGpaeR3W4qf/9zAnPmICkK6b/eCbI9wW/oadKJBHoigaHbXyGXx5s9hzcQsPd5fXiDQTyBEN5gELc/gKKqKJleriJLVE2dRmNzPfmlBcRTA0QH+knEB9ATCcxkioueuox+KQbA3PYcJqlBLFlgSSLTGhcICYRkp3mePHLdueQXe0jHDBRZRbEkFAMkI6M8kja449zfUFE9EZ/mY+MjD/HKvntGfVafn/95SidMAuC1/odZvfquUfNeUnMJY2pnAbC1bQXPj/LxBZheMoOakrkAbBftbGpuOWY+CajNnc602WcCsCf9Ek/2vzJquactuZhpY+zG2cGODWx8dMWoeZfd9HFmTpoDQH2/zsNPjxQAgx0YHTjt/R9m/sVXANBed4AHv/91vIEQEcOgJZ3CSKcx0mmEsDjzgzcz54JLAWg9sJd7v/75bJmyoiKrCrKsIITg1Cvey4JLrwKgo76Ov3z503bDTZIYkBX2qAqyrCH7vcw572JOu+p9AER7e9j94+8wIxqlsAE0Lcz+G65BkSRkRaFy/CQmzLTvbzqVZOara5jT50YIgVVRRfsXFmXm8SRKJ05m4qIzEMkkqe4eFjz3BKfG3EiShFXopvkqD5JpoWkaOYEw5YUlGcUpmfdVV3LTxIkgBKkxrUQ9axCWiWWYaIpC0OVBmAYYJu+fOZ1bZsxAVlXM5lb6O/+KZFmZ4VYJeViD68PLl/OZM5diGgbJuoO0brsdS9cxDB1JN/Dqg2YkMtcvvZRbr7nWflZtbbRs/iqr3/dLTJeLFStGf/5vJY5gdBhB067tpJYsQn3uefTmZjp//guKv/LlUfMLw6DlC1+ktK2bQwVh4gji8RjrH32A9Y89yJjaWdSefS5y9XT6Zp2CtH0Dv/7W1xFSGlVyke8ZgxWaQcA7homSm/dKw15Jl60AZFg6A2YvCVcCKU9BUeN4QjITr78ONatcUXvC13jG+z/MGe//8FH7LdMknUigDFN8OOP9N3LmDbegqCppM01ztJmmgSbaYm20xdpYs+kH2fX2eDupiZl5xyBQcPS5JSQKfYWIJRVEgxUU+4rJ9+aT78m3BaEnlzxPHt9259iamyfJvEuuYNZ5Fx21X1gWwrLwZAQ9QO3Z5zJ+/qlYlpXpwdvzR2TmkvzD5pinLjmL8fNPQ5JlZFmxU8W29zP0NNqwxkTNzLlc+fXvYaRSyIqCrKqoqoYlwauvrmdM7exs3jEz53DdD36GoacxdSOT2kIpnUxSOmFyNq8/nMPkRWegp5LZ6xEZm0lhWQTyhm54ICeX8fNPRVE1FC2zqBqWYRDr76Ogckw2b6yvl1QsRioWO+Y91dND3TbLGKkxaZkGlmlkt019aJxYkoeNKwpxVN7BhhhAOpGg45A9jdHU2X5UHTwFhUyvqbHr09PNxqceO2ZdAWr1NJMWL0XyepFyw2x6+vFR80467XRmfOiGzLWY3HXtpaPmnbBgIZd8cqhRcPu1l2KZx9YgHTNjNld9/XvZ7V/ccDXp3/3smHkrpk7nvd+6Lbv924/fQOLJB3D7A7h9PlylOVwprHdUWDmC0QGA3gceoPuxx3hCRDFNk4mnz6fmgX/Q8+c/k/Oeq3GPHXvM47p+9WsSW7bgCQS44Sd34CovZee6dWx4+kn6D+ykYdtm6rdt5k8V1zFNHs9X1T2MD06nNDCRYk9ldnI/iwRaiR9XZTC7WNYAxV0deGfMeGsv2kiDrGa1PWVFwXLL7O/bT1OkicaBRhoHGmkaaKIp0kRrrBVxApZ9+e48in1FlHkLqfAWUu7Oo8KdQ7kWoqz8FNyhMjtjvAf6m0Bxg+oG1QOyAol+6GuGcAX4MsKp+yDsfw40L3jCRyw5dqqoaC43mh6F9u0Q77bPEe8GPT6Ud8wiKJqMxx/A43ZBOgpmGtKxzBK39/XEoaQW8mrQ3B60RAdsewRM3c5vpkFYaK4AuANQdRqUzyGQl08g4IXu/SANCXfdMCjwJnDHGkDVIVCINxDEW+OFVAQUl30fZPUIzwQ2JeMncuGnv3hCj7agqppLv/BfJ5S3cvoMbvifX5OIRlBVDdXlQnW5Ud1uVJcLzT00WF82cTKf/tNDmKaBZRiYhpEReCaSrODxDzU88ssr+djv7d67sCws08wsBpY5spESyMvj4s9/ndfWv8qM6dMRljnUa7UsisdNyOZV3W7mXngZlmXaaj6D2t6DUxfjJ2bzKprG3IsuB2HZ/hiEbXZjGjrpRIKyiVOyeY10Cl84Bz2VQpZlpMwiK3ZDaHh9hRC4PF5Mw8A09KME5JEaseIYPxtZUeyGizpSAzcdj2EaBvH+PuL9ffY1n6CW7luFIxgdAIiufIHWvbsxx9kf7X17dtC+YDrnLL8EV6aleiQ7VvyD5ofuowpo/vBnuHdLhI2PNbC7dQBLLCFUMYtpA7uoQeU97kLOqSonv3Qh+cPKUMJuXFUZIVgRRKsIILuGPqZGby9NV1+PGYkw5i9/wTNp4shKvPB9KJ0JUy4+4Ws1LZPWA89Q/+hN1Hu81IcKqXd5OESaDn3guMf6LItK3aDUMCgxTYoNk5J5H6Fkwvnku/Npe/QOTtvz+9ELeO89MCgY61bBQx8aPe+Vf4Rae1iO9p3w9NE9d0NoJK0gqcXfxpx8OZYpsA6uw3zhx5ioWELFQsUU9rqJijk1hFXmwzQszK5DWFsfxhQqJhqmUG2tWUlHRkeZoKNUgqJKKJF6lI3rUNAz53ZhCA+6cGMIN0ZFIXqeByNtokd6MRtfR8ZClgxkTHuRTFa9+jBy6XTk8hnIsoQZ7cHc9RSmcGGiYQgXJplFaJieQpRQAS6PiqbquFpexiUlcMkJNCmJS07gkuLIGFhjlmBNuhjLFIhEBHPNLxBCwcJekGRk2bb+UEqmoEw9D1WTUSQJ5bW/YigCRQYLDQsFEw1LKFg547AqF2EaFpauY219EBkDBR1VSqNIBooibKuS4nEoc65BUWWQZMRLP8NCRkgaQnJhyS6EpBGVXXT6ihCVpyBJoHrGkc8mQpaM5gkgBTUkRUUWBiRTtG/agsitwbIEE069EtGwAYGMpXgQsguhuFHcHhSPl7Z9nSgeD7IqMevc96EYA3YdJRPL0DF1C9OSMUyFjr2HMZUAhmFx/qf+FzOVRrJ0VFlHlXRUOY0qpVClFPHtq1AnLEZ1KXzizr/ZDS9ZRag+TMvCMg2EAFmWs7auADf/8o+IeC+KpiF7gkiKx9Zi1S0sU5CM6bg8CrIic/Mdd5GOx0nFY9lFdbkxDOOo9//twhGMDliJBLFXX6U3bM8lFo+dwEB3J/39ffzjucf40PnnE8izxZkpYHtzPxteXkf0yT8gleaxPm8ydzbmQmMDYNtTnxXwcaEnj9rwGHxx2+0ZFiCBqzqEd2oB3ql5qPle6NgNa38AUz8HrpGKI92//R364cNoFRXI/iPmOvc9A6t/Creszv4Ire0P0Ve3kq6q+XTl1dBtROnqraOrZSOtZpxDskVjpJG0lYbiQREdA31oGC1sWlR5C6gsP5XKYCVVkU6qXvkVFbpBvmUhhcqhfAEIy+7pjL0QSuag6zoRc6QJB6oXNA9oPrtHqLgwTYtkVCfZ60JXF2EYFqYBpgmGpWDKAftDtdWD2VyPnjJJduSTtG4nmdZIpVWSupuk4cUQGXvTvwNszJzUB3xn9Af+KsCBYTuuGD3vVmDroWE7bhk97z6AtmE7jtPDPwQcGj4PuXT0vDowMHyYc+7oebuATXuH7bh69LxtwJbhec8ZPS8Ae4atzxs923bg+U3Ddpz+BuUOV0SZysGdAInMMpw40DNse7AXpWeWYw8Fv/WsBkBWJCQrjYzd8JEkC1my7HVMWys5t8pugOkWZnwA05QwGd1GWtFkXB4FzehFs/pxySk0OcX+19wECnwjlNneThzB+B+KlUqdsJF+bP16RCpFnreAKYuXUjN7HpXTZvDkz39C4Zga4pqfZ1ftZN2Ow+xpbaXvxYe4qP0pVGGx1z+BlWOXMaMsxHn5IU5NSRQ1xyGqQzTTwlNlPBNy8E7LxzM5DyVgzwuKwfGVJ78ADWugeRN8ZBVk7PX0tjZ6770XgJJvfQtXRQUAA+kB6rt2cejZz3EoN8yh1V+m1R+mK9FFT7wDA6BvzXGvWZM1xvjLqPYWU41CdSJOdX8r1W37CKcicO7H4bRP2JmjndDbBWMWQvUiyBlz1FCfEHard592Dp7lnyPWbxGPWiRjOsmoTiKqk+zVSf5aJ51YlTnKB3xh9EquAagbtqP6mNkkGdxeFdWlICsSsiKjqCPT4fsVTUYZXFdlZAUUTbHXM35CTcMatojsx83K7BMCVJeC5pbt1KWgumRU9+C6gqJJCAss0+4V6GmDHTt2MmXSFECye7eWyNQFVNlCUSwUyURRTFRF2L0wbxBTDZJOGaSjSdJdzehpSKcgnYZ0SpBO2Z5lZNWF5HLb1ywLZCNquyhVMiPmwsLUTUzDxMSNqQTsa0sbmJFOTBMsE/tYyUKWLRTJQvb4kcPFKIpk38veA1hCzva6TFPCNDONG6FhSl5M3bLt+PSoLTQYtOUzbds9LCSXG4KlCEtgWRZGxwFkSc44/5EQQkZkbAYlVUMKFNja3rKENNCMbKWRMJCEiSRMTCFhWSqm6sf0lgw9w1QKSwx97mUMFCmNKun2Pc4ptd8LVUbp2g560h4JwIMp3JlRAbsHP4hlCkDDHJRWRw6XmkDHcOHu4VhImAjsUSJTt0joFgkCwNDQLVt6yC1N4p91zCLechzB+B9Iz11/pP2n/0P57T8jdO55b5g/umoVAOMWnk7pp76AaQm2NPVx+JQP8Ne9HUi3/pavbPwrF+bKfGROJ4+3TbV/8GOmcdMVn+CH3Tr6jm7M5iGDb8mj4p2Sh3daPu6JuSOGRyNPP03PX/5K7jXXEJ5TagtFgK698Nw34ULbDq/1lz9HpNMMTK3if9UXqX/mbg71H6Ij0WHnD8pAGJLN9jKMHAsKjDQFpkmBaVHgLaSoeCZjat9HTe54yvxlx1ZusSzoPQTu0NC+QCHmxb8gEdGJR1LEm7qJdCeJdCeIdCaIdCcZ6EqQTppAgOfXHTq63CORwOPTcHkVFE2xh/NUGUWT7fXMtprZdgc0PP7MEtDw+DQ8ARWPX8PlUbNuw/6V0XWdhugWZpxdccKeXY5N1VtWpzfP9Le8RF3XWbGijQsuuOCfuz+mAWYKXP6hfZZlOykwBfIwp+vAiCFPAJKThuZ7j2wAWgLDsDDSZnYYVKSTWMkYViqK0A2EpGJJKhYuhDs0JHAVCUUxUERGIJNEsVJIVgpT19HzZ5FOGugpk3RrHXo0SjoNehp0bwWyS+Zgz9GKSW8HjmD8D2TfL36OV5Jo/sxnCe5aPlI77giEEERfsodGdlXVctvfNvPSvk764kPadaXeHAJGgo3+YjY1FxJQc5nsmsCEnLOxHm1g0CW05JLxTs3HO7MQz4RcO0rBMUjtP0Bi0yZkr5dw0u5VWiXTOdS9h22772Wr3EdTSyuffbgOBfjJnGb27ntgRBkFhkmNrlNTczY1lYupCFZQ6C3Manhqsgqde+xh2qpTh+b1joGhm/R3JOjriNPfmSAesYj3dxCPHCYxkCbenyYZ00c9fjiy26KwLIdwoRd/jjsryLwBbdi6C5dPRf43EGYO/4Yoqr0MR5btuWP1GO/ckYpOntDReQazyhJaZoRgCC+Q+2ZrC9jemBTAE8g0CMqPHobXdZ2D74y1hiMY/9MQQrBhbBm6orB4bxMDL64idPZZo+bd+fImlNZWun0+vr6ll3aPhpBkQh6V0ycWcuakIs4O1BPtsihzzWNu/hzy3baQsSIWqBLeSXm2MJycN6JnOBrhSy6m6447iL6ylt9fcj4bx01nu2ww4M0Ir471fOpJE0XAroleyheezuJwDTXhGmoCFVQ//DFCnXth1nVw7q9GP1HRFHvBbukO9CTpa4/T1xGnr90WhH1tcQZ6kycURkKWwRtQ8PklgjkKwVyVcK5KME8llKPiDUmsfeVlzli6FE0bNCOxgFRmyZwknVnA1kj1F9it83cSIcBIgZEcSs20nVpm5mNp27Ydd12SR18ARGZs0jIgnSSQbLUbLLJk7xOWPffq8oHmt1PVm9UUdnB4N3AE438YA21N6Bmj83Xjy3H98XfMPkIw9sbSPLalmfs3Hia9ezfXlUwjEja4uu3vmLPP48xrrmV2ZQ6KJJHacZD4va8gVf+c6ZlJcyFM3NVB/Asq8E7LR/ac2GvUn+pn9eHVvND4AmeVy4xvtjj4zNO8skAGC7yKh2nhscwoPYXZB+uhbjUX3vZXrpw6daiQVT+Gzr3gL4Tl3z/qHEIIYn0pulti9DTH6GmJ0t0So7c1hnGckDwuTSfHN0DY1U1A7cMr9eCTuvBZ7fisVnyiHY8URZIykQR6M8sRLAMY3QHI6LiCtoD0F4CvAPz5mbQAXAHbjCI1kFkidpqODu1LR+1h4KGAi8NSa2jd1IeE4DuMRsZX6oncH9U7UlgigaXbwnRQ0GaXzLYk20JW89lmLcOVnjSfva247PyDAjsruIetI+zzugP2cKTLbz8f1+C+gF3e4P0crIep23U0B+ul26Ynqtu+HtVt10v1ZOqUSSUFLB0pFSc3uh+pYY3tfsjUh8q0zKHjBk17jkwVzX7Wg6Y0pmGnlj7MxEa3842owxF1G835eMb7j/0+ZX5LijZ6/n9jHMH4H0bH1tXZdVORaW5qYNLrm3HPmsXaA108sLGJZ3e2kzbtF9tVUMnmW77OxBf/F7O/m/desJjyHB/xVYeJbWzF7E0DpwEghyX69r5A6fVnknv2cTQDh9EabeWFphd4sfFFNrZvxBS2vZM8DcY3w0X7g8z+1GeZUTiDCbkTUOXMKzkPzM/1oeTkjCywfA6EKmD5dxGeXHqao7Qe7KezaYCe5gF6WmKkk8cWgDI6YbWVXKWFHLWFcCbNVZvxSJGh33dGhmQZHgVAcdsfyMGwSWJQGNnG5oauo6oykhDDDuKIj8ewdSOR6U0N2EvvCcxPvuVImY+r276+wWtDHHGNR64P+0gOuw8jPpySnLEVVRGSjG4KNLcHSVZtgSDJtqDW4/YyiJGwF7pP7lLS0bfkjrwbqMASgP3vckWy74B19HIsJBlkzRaSmWdtr2sZrSfliJGEI0YcZMVusCiujMDOpIp72LoL2V8EVLwjt8ARjP9hdO7ZPGK7K+Rj5eOrue3ZXpr7hjTEppWFeM+8Si6dVYaSiPD7R3uo8E/Gu16i7b7XsoJBUnR8ysv433sNTKhhzyNbmJR+El7eAKd/niMRQnCg7wArG1fyQuML7O4Z2T2YkDuBsyrP4qy8Fnj+UfIb+5mvzMGdP2FkQU2voTz+abj6bii0XZgZukkH82ituYfWFT20/e55Uumjh24lTHLUZnKVJnLUVoq0g+SrjYSUNmTJslv72V5ZOfhmZnpq+bZBvTsE7qC9uAIj11UXRtqkuzlGYVXA9v6SwdB1VqxYcXLKE0JAsg9i3ej9neh93fhEJ8S7INZtp+lYprcSzC6WFsRUvJiqD0vxYSoeFE1D1TRUzYWsZrxPS3agWElWMi63XMN6GK6hnsYorf5UwiCdMLJxA20vM0PPOlzkG32u9AilDkPXeep498eybGGYjg8JynR8yJRm8KMrK8PWh+2zzIyQzQjarMAdtm2mbYEsK0Pp8PVBhwR6POPsIJrpmUeHOUAYsNOsQFAzQkAdEg6DqWXa16QnM8PWmfoZCXsIW0/YPVXFhZBVYkkdfzCMNOjoQHFlno989ND3kengj3ZQSCmaffzwbVmze5Aj6pOpwyBmilFCIo7ynK3MMak3zvtPIBdMgsqvv63nGMQRjP9hNGfssiZrHezRi+gI+PlerBozkSDs1bhsVhlXz6tkenmYVN0h6Gynad0hziu/kZArn/RBW7PUPTaMb36JPVSaroVAIX1dUTp25dFn/pb8Um9WMAoh2NOzh+canuO5hueoj9Rn6yNLMrOLZnNW5VmcWXUmlcFK+4e8cgZNpSmizV5aHn6azmkKtUsrGPjtzwkuW4Z350+JtbbS8dvv0ha+lNYWifa+8AiVc1BQpSQl2l6KtP3kqw3ke9rJyVOI+SbxQv2FbO8/lfde1k94TIWtgOMryJqDvBFCCNKJBKl4lGRHH52NBziwsZHmve3oqSTBfJWaGTnIiomeTJJOJmhrqOcfO1/H1NOYhpHxNGL3Jq2MGzNDNzFSBqZh2uYPpoU1GAgY8AY1VJecNWcRwo2pJ7CMAdvTiK4jRmu9DyLJSFImjBAKoYIAqsuFomroKQtDB9WlorldaG4NywLTkLAMKKwKZz2pHN7bR6w3xVC3WRoh7KYuKkNW7O2ethiJSArNo+DyqLg8KrLqyoQ1Uikem0v/gf3sWeuneXeUvg4dgWp7hbEshGnfCyEEi68an+m4CvZt6KGtLpJxSi8Pi+tp9zgWXTkBb8CNJMvs39jD4T39GdMUDVkJIqs5tl9bVeX090wmWBBAlhV2vtzM/o3tttlIZsm6xJNg2Q1TySm235W969vYt77N1hp2KageGXVQm9glM3VRGcE82xyh9UAfjbt67EaEBMgCyTNkNjNxfjGhAtuFXm9bjLY6u74Ciy1bNjNvwVxcLg1ZkcivCODNmDelk3YjJavlmTGtEUJgmRlTF80FkmS/Y2krq+H8hrEZTd0WkEbKFtrCGtbLO6LHJ2dSITLD0Pqxh5EH14/V8xwc3hdWZhg6M/w7Yr47ZS9mCow0lid3yNP/24wkssZk/5lEIhHC4TD9/f2EQqNrW8GguvRJtvj/xfjNVecSUzTOlfexVh5D1HDz2uSrufyiM7l0VjkeLRPBWzdp/uqvsdIVyL6M8b5skHNGDf6qXtQJ0+xhjGGs+M02Dm3pIiB38oGiW9hx81M817qO5xqe43D0cDafJmssLFvI2VVnc0blGeR5jojruPmv8NgniHSV0Z48mxe9l6PrEuW5fWibX2EgWEWisJK4dXSUCK/cR5m2m9KcdkrLBfnVBSjFkyB/PORUIXwF7Hm1nZcf2IeeNDnt8nHMXl414sMgLItYXy+Rrk4GujsZ6OokkkmjPd0kY1GSsRipWNQOvPpvgcxQGHuHN0IGkFQs3JkGhAqSBpKKlLGpy1P7UWUTAcRNDzHTA4isW0AJDSQNSXJRmZci5FPQNBfdsQDNPSG7vOxzGRqGnjImTtBn98LbemQa211D55WUTF0UJBROu2ISlVMLURSFQ1s7ePWxg2S9ZQiByHrOsJhaGaMk10RVFDr6vWytz7PjLUoqsqygKAqqIqHIMHtikupKkFSVrojKll3KMLtWybbRFBKGKTFzYT7VMwqQ3B46WpI8e9eerF2sJEvIsm3bKclQu6SMiXMLwDTpa4/x6pNNdsBpl5qxoR1qTFRMyqV8oq3NmozpNGzvsu+swPYulEhiJZNYyRTjKk2EDC+2t7/p7/PJyAKnx/gfgGUJXtjTwR9f3M08xX5hREBjjNTHzv5iPj7RYkEogdrfixXMIfZqGwMvHwZ1JrIKKZFgd++rjH//UsZMScEfLoHiaXDNvUO+OoGZy8s4tKWLqFXI+eWVND//kezfPIqHxeWLOWfMOSypWELAFTiqngAYaYxVt9OWqqW58Bzq+8ahJzIhqHpzoPqCzEXZQ6K5ajNF2n7KtF2UFkUJv+8nSEXn24oCRxCPpFn12x3UbekAK0puiYGZTvLKg2uIdLQR6eokkhF+w505vxGKqqK6faQTCp5AgFBhDr6gn46GBMk45JfnMHZ2GYqmsXf/AWbNmYPb50NR7WC2fR0JvC6BZsSw+vppPhjjcJuM10rgtqJ4rRhucwBPOoKsp5D1JJauI9IGW4suxZBdFHVuobRtI7orRG/uNLrzZ1C74w/4k91IAtqL5tFYcSbuVDeuVC9auhdN70PTB1D1ARRjIBO5A3rD44j6S0i7AqQ1H4biQTFjmbxRcnt3o1hpQLJFbaZNITJR10drSSe8haTcOeiqHyNTLiKNbCWRrBSh/v12JBFZIqX5MBQFSxJIma+hJEDCHrJVzRRSZp8pawhZyYojka2PHYlEMXU7IgkSlqxgyXLmb3YMQTLrw7EAhAEYR/nxHNzsGvGKHO0qcPhh9W1H/XlUtp2EctZLfz7xvFt3246Kjo8CSLzQbCAJCwkQkoypDGpFD44MyNkGQ/umCJ5UBEUITMVPf3jCsAbE0IS8QBBZ18DmeBtIkNYC9ORNyQj8wcaHmhXWRclGSs1mFEUhZnnYFzg161RVDDYmMmU3H3iQQEEe3HB0LNe3A0cw/pvz4p4OvvfkLuo6Y/j0OB9o6iTpVik7oxZl/0Z29hdz8PlnKbztl4Sv+gKCakQiYzsY7yZ1eDUrPPUYZpozpn4M7r/Y1npEAleA/lQ/a5rXsPrwatYcXsMlrs8TSOcgxSfhcx9kyZhlnDPmHBaXL8anHTFEaVnQfQDR/Dpduw7QdDDB4a4CWlLfH9UtVF7vHqYsDVE8axwF06aivfRN2HS3PTfygTVQZEdbMA2dvrZWeloO09N8mKbdh2je04ip9yGsAcCiLQJt+455GiRZJpCXT6igkGB+IcGCQkL5hQTyC4h0Wbz6jxZqzxjLqZdPRdVcCEsQj+gEcofqbRoWW55vZPpphdDdTqKxkWTfFoq37sRsa2egPcJuaxotgWmUtL3K1D1/AaAss7wRCU8+ckkBlieftsoa2iovH/H3yJRzKJL2Iwf8hP1+JnkOInvcSO4cJE8xstuN5PbY+1xuJI8bSdOQVBVJVUFRbG8qmoqkKDC437IQ6TQincZKpxEpe13o6ex+YZigyPbc5fBUsV3MSEpm+M0yEaYFpoGR1tm2ZTO1U6chI8C0EJaJpKhI6uD5M/XTMnVRVSRFRehprFgcKx7HisXsdPh6LAZCIHncyG7PsNS+flxucLsQioJh6BiGiWnomXUDI+MQ3DDtdXuITyBlnG4PT7EEwrK95+iGjm7q6IaJYRropoFhWeimiWHZvU17dFbK+K+x1zOxTOw2h2VhmgaGaaJnonNYwsKUJCxJwpIlBFKmoZBpQEgZJ9+ShJx19G03ZExhYQqBMSw+5BD25KEdUHh49I+j5wcHGwwJDRLZhqgAfZQfFdDngT7PsDDh6T2j5m0BWmS7SKQExJ4aNe+eslxCikrRqDneWhzB+G9KJKnzvcd38eAmewgz6FG5ZUKQ6icjyMEgeRMm42l5norCEJMLzyeQ92GshAcwUAu8iOQOBh77b/wXns+l7/86HQ2HCLpNROdu6lwuXpp5AS89dzNbOrdgCYvBX3hLzn4mdsznA83VXF7swnPGT4cqJYTt7HrfU0R2baapUaEpPpHmVC1JsXBE/b1yP5UVSUqqg+QW5fDaz1fSUnY6wTFFzPrYVZniBPFTv0xPl0mPUkbvs6/Q03w/va3N9LW3HXeYU1ZUQoWFhItKCOQVcuD1JJYZYNIpE1h41Wz8ObnIytGKO3vXt7H+H7sRVi6xPjtcktnVhd7WhtXaSk9bG3prG3pLC3pLC8HmZup6bP+VAonOqR/G6t5Gyp1L/ZhLsRQ3CAvJMpFCYVzFhaiFRajFxahFRSg5OUOCy+3OCDZ7W3a7mKy5aGgw2fF6lI7DCVSXzJjp+YybXcSY2iW4TtBU5l8BXdeJuDTC/8ZTFW8nw6dyVFVFJJO24I9GsdJplEAAORBA9vvthscJYs9rp9FTqWy0DhAIKzMsLEQ28gaZ1DJNjMG4lHoaPZnCSMTR43E7TSSxjDRkQpBJimIvst1IkgYbSwj0ZBIjEcdIJOz1ZNIuO5WyU11HUhTkTCNtRKrIGRd4Mv7cfN4pneN/n1+VQ5bV+zr58sPbaO1PIklw46Iabj1nItYdn6IZcFeWQngMgnM5PecDWAnb+a7Z34RnkkLRp9/DoUu/A8IkfOZSQjPnEKvw8OP1P+LFijKaNRV2/C57vgmhCZy17mYKa/zohRH6O0DEZuBpWWFrt9WvwdzzNG3bDlDfXUV9aj595sghD00xKC+OUjHwMFGrgDr3lcy/cR7+XJUt8+fhzq3CyAtyKNXPoz/ZTaSznb6OdvTkoCZt/VH3QfN4ySsrJ6+sgkTMh+rKYdY508ktLSWQl4c8zOXbgU0dPPP7HRzYDOMXWIzLH/qbFYuRqqtjy/NNvL7XVqAoM+oY++CP2fvLVsSwGHujIfv9tI87i468OXQUzcnuLyqSWXhBOaUzFyF7v/HGD/cYTJkGUy6AgZ4knoB2hNcRh/9EJElC8nqRvV4oOEZgz5MpS5btsGHDwmf9OzLYcHgncATjvxHRlMEPntzNfRsaARiT7+O/r57J/Gp7HnDjptdJ+zwEKufS9tw4TGM8AGq+ByWng67/+T7pnYXkXLqA1P4DoMg8nHuQRx+7lEP9Gfs5TUVDYkH5Qs6oOIMlFUswGz08/sxWogclck8xieyGXrOCzl4vvd+6jvr4DBpTS0iJC7J1lSVBcaVGxfRyKqfmU1QTwuxtZtOfdrFxu4plPMI9X/s9yVgfTM74vky8AMDB4YEJgFBhEbml5eSVV5BXWkFeeQW5ZeUEcvPfWNsuw/i5RbTtLWbr6nae/8N2RMl2XI27SB08gN7SxoFxl9FUuQyAyqaVjD/4d8yszYqEWliIWlqCVlKKVlKCVl6GVja0yOEw1Smd+l+uJHrQjS/sZuEV45gwv/iE6/hGDGo9Ojg4vL28q4LxRz/6EY888gh79uzB6/WycOFCfvzjHzNp0qRsnmQyyec//3n+9re/kUqlOPfcc/nVr35FcXHxu1jzN0/KMDnQEWVv2wCWgOnlISYUBVHewG/m2gNdfOmhbVlbxBsWVvOl8ybhc9mP0Ir1sE+dS+0ZZyK5SzAHQJJjhKt2o1x7Ey17dJSiQoyODnZ+6ia8wM4yi5eeeBAjJ4WnxM1SdwHnNWzhtGnX4Vv2P9lzv7DR1hYYW9zM2H1/ZLX0IaLk80DnbcBQ78XjEYyZUUj1zBLKJ4eJdbfSemAf21c+SeuBfXQ3NTBcCTqZ6YgJTWZAS+IqLGDR9LMIF5eQU1RCuLiEUEERqmukduwgm56up3RcDqXjw0cJH6Ozk+SuXSR37SKxcyfJXbvIbW0nPPMz9OeMZ83efOa9vh7FSrNn0vtpLbWdGEwP1jH10mpcpT9BKy1BKy1FLSpCOoGhP1mRCE9Ic+mHluAPeVC1f65nZ5omyWSSVCo1zHRDjLo+ON8kZcwZ5GFzUFLWxAGsjHmENcyMZPi62+0mFAo5w50O/9/yrgrGl156iU984hPMnz8fwzD42te+xvLly9m1axd+v+0Z/rOf/SxPPvkkDz74IOFwmE9+8pNcccUVrF279t2s+hsihOBwb4K9bQPsbR9gd2uEvW0D1HXFMK2RanBeTWF6eYgZFTnMqAgzoyKH6nwfkiQRSxnc9tQe/vKqHeuwMs/LT66cyWnjhsL9pg8P0HX/ZpaUvg8AXQgOJC2YXsGZHzyLO268BiOdJnxKDose7yDe3sKvLpeRTTczDoaR3Bo3ffpPhB79GMQTUDDJjm7fsBZz99McfPV0wEdrfYJd5ldH1D2vUKF6TjnVMwrJLdWo3/wau1Y9wVO/2Ew6cWQ8OUAKEiwYw6zl8ykbPwnyfVzyzFXoQgdamXTqFSydeNEb3t++9jivPmqHZFo1/Wd8JGchs3qCtjDcuROjs/OoY2RgVufjvBr+KLFAOQ1X/4ilFxch+nNof6SJs66fzKRTj+1X9mTwhVwjhKKu6yQSCeLxOLFYjGg8SjweJ5FIZJdkMmkLwWSKVDJFMpkknX7n3bYNx+fzEQ6HCYVCI9JwOIzf72dr71Y292ymIlTB+NzxjM8ZT9h9tInNIFE9SqPRyEP7H6Ix2sj4nPFcNv6yY0c5OUkGGwhvVe/83wEhBAMDA7S2ttLa2kp3dzeKouByuXC5XLjd7mOuq6qKoijIsoyiKNll+LYQAtM0MU3TVkzKrA/fZ2Xm+Ic3zo5MhRDouq3gNDwdvm6aJh6PB5/Ph9/vH5H6fD7cbvc7/lzfVcH49NNPj9i+++67KSoqYtOmTSxZsoT+/n7++Mc/cu+993LWWfYH66677mLKlCm8+uqrnHrqqe9GtY9LNGXw5Ye2sXpfJwOpY5sEhL0ak0qCSMDOlgjRlMFr9b28Vj/kfDPkUamtCNPYE6epxxYw7z+1iq+ePwW/235sQrfoW1FHbF0r4MIUBi2pbexI1ZIWcNo4Hw/WPUxnOEVup8RKbwez3KChcObZH6IiXsrGhnupmjSdkCcEp37U9gCz/1nMlT+kMTKezbHLSFu2tmmvOQaBRUG1ytRTxlFdW4AnAHWbNvDaow9waPNGDH3oY665PZSMm0DJ2LEcXBclZoyjZFwpl352Tnae7Jebf4kudHyqj7gRZ3PHZi6puoxkTCeUf7RJBoDR08PGuzcBCv7Idr77y4PAQbqGZ5IkXGPH4pk6Fc+0qXY6eTJKKETe/l4ev30L5Z4C2ltlRFWE6R/30Bbax6H6rUTTcTqjUaYXTuT0ypHvmBXXSdVH0FtjIIOkKrhqQqTD0NraSldbJ/+49xG6+nqIxqMk9CSGdTJuRI5G0zT7w2BlvGgxzF4BSBkpdFnHxLI1HYWEKlRkSUJCti3vjrBJMCUzY48nUFAQCPyaH0VWSOlpdFMnHo8Tj8dpbW09bv12sYsd0g4MWcdQTVRNJegJUu2rwuVyIcnwSvsrRM0BTMnEbLSIKTFWait54rUnuG7StZRIxbZG6KB2ppn5cGY0RnVDJ51Ok06nGUgMcLD7IJqloVgKsiUjmfYzl1QJj8dDyBfC7XajaArt6XYUTbFjTSpg6oOOFUzCapiQEkLX7fI7o53IQsblduH1evH7/IR8IYL+IF6vF4/Hg9frxev1ZoXJ8J75kQuMLjAG1yVJQlXt3/Sg0FFVdaTtrRD09vZmhWBrayttbW3EYu9UcOJ3D0VR8Pl8FBYWEg6P3vB6K/mXmmPs77fdGuTl2XNmmzZtQtd1li1bls0zefJkqqqqWLdu3TEFYyqVIpUaUj2ORGxPLoOtlOMx+Pc3ync8vvXYDp7cbn9INEViXIGficVBJpUEmFQcYGJxkJKQe9iwlqCuK8b25gjbm/vZ3hJhV+sAkaTB2gO2n8iysIcfXj6NRePygUwLrCNO/wMHMNpt/5ID0m5WNa0it7iQtKhFFMf4dPsH6G/qZ0ZumNzOHOaYE/H8/HLGzzmbOR4PT9/xMwBKx1RiPvVVpE1305koY29iKfsT7ycphoxg3T4Fz9wYv4j/gMVV86k1zuTF79/JoXYD0xr6AYeLS5mwYCHjF5xGwZhqZFmh+dkn2W6Nw6UkWX7TZJAsdN0iYSS4f+/9yELih+O/jSjUKG+fwp1fXEPF5BzO/cg0AMz+fhIbN5LY8BqJDRuI1Tdz4LQfgKIwI9KEXDSFQ8lekmMnMm/Zqaz07eVxeQeX176PC2ouIJKKsK23jvYtj+EuDnDR2Is4/zO1pB/cj2ttCtdaUOUEW3372OLfwxb/XppcbQjLz4/m/YllRaXE17WRPhQh2tlPP3F65Si9UoweOUqfO0FSH3rnmo7xXkhCwo2Kx+ulQWoiJsUIm35OjdXiFhouVNxCxY2W3fafVcHzoRYOHi7i1vHF6PeN7kRzx8xWAovLeKHpBZr2HOBb+24+Ks+gYfqdRY/yaP5z+FQfl3jP5YMbMpHrE0P50hhEpSRGbQB9rItIJEJXWweRA13ESJKQ0hiS3WOQkFCEgmIquE0gBelomn0cyJ47SA5Bco6ueDus2X/yoz9+/EfvFCB0QUJPkBg4xmjFMejI/DuSdDRNlCidHD3y8Hazbds2wPYaZRvQ24J3tBGEvJw8SguLKQjmIUyLZCJJb7yXdEDgFm4M3SAdS5JKp9ENHdMyMYcNp5uW3QscDVmWURXV9iYkK6jaUI8T7F76YA9TCIErM/UxOHyvqiqaph0zVRUFGZlUIklcT2YbY4PLYG91YGAAr9dLOBx+09/nkznuX8bzjWVZXHLJJfT19bFmjR249t577+VDH/rQCEEHsGDBAs4880x+/OMfH1XOt7/9bb7zne8ctf/ee+/F5zsxV2Bvls3dEnfvU5AQ3DTJYkqOYJgrzRPGtKA1AY1RCd2CUwoFWa18AQUdbirrfciWhK5Z1I+L0f78T2nW/bg8s5G9Z7Km+iF2lL5MhW6wrH88bBDImouaK6/Pxmds+Ptf0RMJLq3aR1qqZWv8YnqMoSCwkstCGLaVdN6sHlqjG6hv3Eh5pxd1mDB0+X34qycSqBqLKyfvqGGPKbv/RFPbVBLBQsx51dn9+/X9/Dn2Zz7SeTWXdS2lrSxBfW6C9rVBwGJG4gECjQdwt7VlnHLbNFacxYHxVxKkk6U5JTyb28sPelUEEmdrMtepPezx1HHA00ifEuXUaC2LBmajCoUvTb6DCYkbWdksMzutsdx/iJmJsQStod6pgck6z35+R5SQ7me+P0VuUqdfipOWRncM4Ha7CaheSmNhgoqXuJrkoNxAjxolGV3KQUNin26RkNPkugRLwhKX+114UgqutIIrpaDpMmkL4ibc7d3Cmoo7MeJjuMo8n+v6phCVYhy22vBQQonPiyULLEXQn6sTDdl1c0cl8pokdD1FnsjBZarIpkRaSjMgxdhSVMdAiUSVWoUv5aLqkA9Exq7OAl3odBtd5EhhYsUKbSUxXkm9wu7Idn7a8gVckgshoDcNMdPE7zJQZYP23Bh7c5rp0/vw615q26owMDEwSQsDEwsDC0MyiWtpolqKmBkjKAcIxt0gBFEpSVweEgA+4cKlwNac/Sz0LkSVFMbVhdBQ0ISCioqGgips04B1nibu97VzeVEFbmFRecBDGgMdk7Rk10FFQRUyve4ojcU9lGvlyLLM+P1BXEJFFjJpySCFnk2btU4aw90UikJM00SLZlyxIRBSJs380xEksdAGGw5CylgMSsiZ3v5gr99CkJCTyMgo4vhDyrKQkD1ehJEaIcgUITPWLGaaWUFBpjHbpfZx/YSvARCSQvyo/lOMjZeTIE2b3EdaMqg2C3GjYSgWW+bbo1VCCKr3+gj1ayhCRkEeGqEALEmw+dShka2S3Rod0S72K61EpATTRRWzGWuXBeyc3Z91EFFxyEdOj4ZsSZmFEWVvnt+DlfnOVdb5yO1xkZINElKafeP6MBWRnWJ7M8Tjca699tp/L883n/jEJ9ixY0dWKL5ZvvrVr/K5z30uux2JRKisrGT58uUn5BLuueee45xzzjlpxYPW/iTf+OUrgMFHzxjL55ZNeMNjThYrYRB5tI5UnW035xofJv/ysXREX6fl6YzhuTYJgUVj7i7OMifzs8PPYs0+j9/saCYdjzNvyiSKi4LEVv6MP2bmAF+Kfp+ksFXCVRWqZxUyYX4R3mCKVx56jpZ9r9P6UhPCshiD3bjwhAvQU2PJ9YS59pYCqH3PsSsd60Ld+mEm5qxEv2UdFIy8L1ftOQv5XturSHGzh/A/7mRtyYXEAuWkGww8bZne99ix+ObP59DYXLZsLCeow6TcMLKQuWLadKZMDPD5h3ZQFRFM1KuYmDg6yvuAmkTpOI2HuuwP0PaATG71DA5JcSbKvUQ7O+js7yZq2vflDACtHyMNncMaOOFwmIKCAoqKiigsLKSwsJD8/HwMy+LhFc9iTEtz++67aY4128/N8BPvn4bQC7HDJ7npTYJnejmLLrV7xUndZPb3X0BTJBKZ8FguzyHcQkH1NfAov2FNUQFdCXvAWB+YypnhL/KDy6YS9JzYuzo4bDfzDfJ9d/13efTgowBMzJlIJB2hLdkGLrjnzFV8dMpX+di9W9jVmvEGk4Y7PziX5eMLWH6cckf7fcViMdauXcumTZtsJSAE7qCb1ECKQ74mNhStpyhUzPnLzmf3ht3kzCqkoKCAYDCIy+1GkuD+Tc18e8UeRLIYK1lMSvZx1/VzKIxbiLSJlTQRaRNhiEyABwkl7MI1dmhoLrW3F2Fk7PosAZklbaQ5bLVy4ynzso2+5LYunt3RztO721FkmVnlYSQ9jpkSdJsKL0g6X1ocZevKzQhJYGJ79XGh4rHCeIwiPFaYLuBPuWs4tyiCp1kjYAx5jPJbbsqsPLxoTDBL6Qc+HFjJTdOryPV4Kd/g41CqnU45wn61lf1qK+VmKdXGJJpd7QjTj1eYhJNhXucQG9yHiEpDnYxyMw83GlES/KrtXqpzC8gtDDJGWYJbHN2JEEBalalzj2d2OE7j/t1sSdYx6L5YETKT0qW4MwK+We4hltY57ZRTqCgroe++faTaekZ9P3KmnsLU8hAhr0bfA/tJtXej4SKAi2lXnIkhWW/6+wxDo4cnwr+EYPzkJz/JE088werVq6moGAorUlJSQjqdpq+vj5xh4Yfa29spKSk5Zllutxu3+2ivKpqmnfDNPJm8AKYl+OLDO4gkDWZW5vC55ZPR3kxX8Tik6vvpuW8vZn8KFInwudX0z4YvbvgK6w++zDWi0m71K4X0etu5dvM37QOLX0CLNlM1bSYHXltH82P/jS++hRfbTwFAUopJigKCAZ1a/kzJWGjIu4RX/nYn7XUjh+4KqqpZ7znIzqJ2vrjsEzT+n4soEolDLxGaM8r92vsP20lw2Wy00qmIdJrYa68RXbmS6OqXkcOnok5YbntAkRXcU99Lcd066gLl7Jw4n7GfXUblwnNQCwsZSOr88v9+yWI9j7Fei3LhR/aphM+oYlHQxdO35vHD+7bynf09TEJhvtdDtUshMDGPgTFBrnl4A6GeChb725mRZyHF+4gdiJICth9Rba/XS35+Pu0pjfWdnaQKViF5Qtx91c8pyTnaruyVgx18+ek7iXhWYG20W9R5njyKrfOYUXg+E2fkU1Pop6bAjwTs74gS9g69Z/s7ExiWwLAEBQE3508v4fzab1JdZHDf3nt4YO8DdCW6UGWVWcHLeHnfTJ463M7O1gF+8b7ZzKzMeZNv1tF8a+G3mJw/mTu23MG+PtvLSbGvmFvn3kqRdCpX/nY9XdE0+X4Xy6YU0xtPs3RSyehRNo7gyN/Xpk2beO211wDo8HawLXcb/e5+3LluSlwlfGXBV7ls/GVE+6NHKd15PB5bg9brZ5pHZtqMWby0t4NDXTFu/sMq/vt9C5g2vgjlOAbxlmURj8dJlSpHKYjouk5eXh6zShfyWn0PRjpJ1/5NrD+8nhr/OCqKUxT7FeLRASKRCGeeeSaLTl9IztZfc9vmv7JMWkbKkyJoBO1hXgwScje4uqmetYhxY6byA3MaoZ7dvNrwCsgQyU2wzbWFfs8AVxT8jp4Bg8cHkuyzfoXbs5m/ZGRLoNSLLhn40gHG90+kIlbB/ZabAyIG8VLm1X2Q6Wo7AH2ksj23PtmkTy/gPNIoJMkZ9x3mdc6ltK0Ia7fFX7WVmGEDS7IwLBfxRA1r9LGYgGoNcNW6F1klDfVYZX+Q3TGZPrOAdUJCwp73nKjUEd7Vz75dO6iurmb2hFo2SgYrdreREhJtIkQKQQool7sRfznEJ5dP54xplXiWlXFwXJCV29so9bm4HIlA5p052e/z8PfuRHlXBaMQgk996lP8/e9/Z9WqVdTU1Iz4+9y5c9E0jZUrV3LllVcCsHfvXhobGznttNPejSofk9+uPsj6Qz34XAr/995ZaIqMMCzMaBprQMccSI9YtwbSmFEdJNCKfKiFXtRCH1qRDyXHjTTsAyMswcALjURWNto+JPM9hN47nvv6H+E3j/2GpJnE5VKZnIiidiVom7OeVinO8u5K1scMOq08SnrqqJLDHAA2bU+zMfDf6PGXgCb8eWNYeFUFifU/YcuOVnqafMBfsudvz03SUBzn/effyrlzLmP7PV9ifDSHHQNbmVgym9Y2jca9caYf477oKZN1T/QyOV2NJzmHgc99nujq1VjRIf8VnnLbydP6+heZVLmQXF8BO2rPwdcLkjyBbxn38Me8q4mmDD545wYG2iejuNNMddtDKjmXjUcJ2nMaOT4XP/7wPO7b0MQPn9jGQ6lOrhjjoUpv4PCqw1zuzpzXhHhm6kiSJAoLCyktLaWsrIzS0lIKCgpGDLv/ZNUK/lx3P5LSwI0rb+TO835Hsd82F2rrT/Kjp3bzxL6X8I25B4CwK5ebZ9zIeya9B696bAWi/MDIxtuU0iCvff1sOiMJJpXmjDDf+fy8z3NT7U2sbFzJ7KLZ1IRr2Dynl0/dt5nGnjhX/eYVvnzeZG5cXDPCJCOVSmU1YQFKS0tP6OOgyirXTbmO82vO5+4dd5PryeWaydfwzPYe3v/QeiRTZ3aRh68vH0OuVyEQLMU0DWRZI6mb9MTSlOUcfd1CCNLpNIcOHbKHLsfbdrYLFy7k8OHDLF68GLVQ5bYNt7Gvdx8fmPoB3jvpvXhUT/ZZLViwgPr6evr7+0mlUlltXoAb55/KpRfW0tqf4Kbfr2Z+dCOP/Hkzf5ckfD4fwWAQTdNIp9PMnj07q6PQ1dXFr371q1HvR/mkmbwUK2PNgS7mlXmY3rMJFZUmbE3xtkzEB0mSiEajCASvtrxKXIkjnSXxnVO/g1fx0tPTw+HDhzl8+DDNzc0sXzCNsjLbOWBPj5/C/HwaGhq4+OKLsWSL+v56JuUNma7du7uOPT3VJI0kSTOJKUwq/ROZEJzNpNzpWKk0H/B6cbs0ZFlix9YtvLrqWUpLSxkzZgyWv4BugiSFStq0WKzIqDJ06bcRS+9EFgOQNAjpI0fWZG+M2WdMIJpO8kDnB+htO4VQOkRjsJGGQAMxzX6/gunFBKPvI2mYJNIm0XQpNaZCjdpLfX099fX1ANS6oE/4aNPmkO9W8LlUZkZ24jbj7FhVx45VDN5QYpZKveXlKmUq7yTv6hzjxz/+ce69914ee+yxEbaL4XAYr9f+YX3sYx9jxYoV3H333YRCIT71qU8B8Morr5zQOd7u6BrbDvdxxa9ewbAEP7lqBldOLqb7T7tINx3tdPiEUGW0Qm9WWKbq+kgfsocAfHOKaFwY4zubvseBPluxYX7JfL5+ytcxznkf1sAAf/rKLD655+MA9BmCqPer5KdDrI9cQHfSRFarUNQcqmtz8YcP0lG3maZd27Pu1RRFpmrGHCK5AR7v28TBwr18Y/KXOG1lNfuq46w6uJoF+jjun/8snzI+y/rn+6jxvMYF//3ZEdE49PZ2tvzxGTY0VuBNdHDq+u/aDqIBkRvmhaoIxqI5nL7sB/zygfUElVaWz1jEvm0xfhmLcm1UodRw8XLNg0w9vYItWxfzekOUfI/Gw/5c1O4k3toC8q+bgmmadHV10dzcnP3wdHQcrVAxKAQHBWBZWRnFxcVZZYHjcc/r6/j1rq/Rb3ZR6i/l9qW/4OHVe/jbFjfxtIkkCcZNe4hpShHfu+y/CHmP/64N0tPTw5YtW2htbaWlpYVYLEZBQQFVVVVUVlYyduzYUTXxWjp7+fEjr3KwsZlcOc6MU5bwxYtmAfDkU0/z2vpXR+RXFIWKigpqamqYO3cuwWDwqDIty6K3t5f29nba2+3explnngnAxvoe/nrX78mRjq3YEg6HaSxdyur9Xfz2+rn4E+00NTXR3d1NT08PPT092bmxkpISPvrRj57QPToWmxt7+eRfNvD15dVMzFWIRCIUFRVlR5z21x/m7j//GdVKc6x+7Gmnnca5554L2Ep/t99+Oy6XK9sbkRWVgbSgI2awLxVmt1mMKktcPaeU+d4Gnm1+mrZUG5IqcX3t9cypmENxSTFej/3daoo0sbN7J+fVnHfC1/RWR/cZtH/1eE7MMcSg5mtbWxumaZLW0yT0BAF3gFmzZrG3Zy9XP341mqmhyzpezYtH9eBVvXhVL2dXnc0nZ38SgEP9h3j/ivdz+fjLubDsQpp3N7Nvnz36oCgK+fn5XHXVVdlzP/nkk7S3txOLxYjFYtnGDoDkDfOtL3/2n74//zbRNX79618DsHTp0hH777rrLm644QYAbr/9dmRZ5sorrxxh4P+vQDxt8Jm/bcGwBBfUlnD13Ap67987JBQVCSXgQg5qKEHXiHU54ALTQu+IY3Qm7LQrAYaF3hqzzQEySC4F14Wl/Nz8Ew+vfBiAXHcuX5j/BS4eezHGz07nwMAAyDLrpUY+mTlOlmBrz60Ypm3z6AlAzQwNxC4ObnyZeH9f9hylwRTT/Q1M+ugdvBrwsfp3azmr82ZqzSYW9U3AiMVZV7cOgA3aQWhOU3ReBTzfx+HUdMzWHVhqKZFnnmHgqaeJb93KzvlfBz+UN6/GXVNN8OyzCJx9Nt/qu4enG5/h4rFVaEY/Y9gFJiT1ej7z9SsZt62FdU/VIxqSTOidzI4Na/Fau1jqWcDioI+n+3eR9piYbRKJH/79De39fD4fV155JZWVlSckBMEWEB0dHdmW7uGGBr5x2lf4ZfcvqY/U81/3foWZPTMZaxXjqqzlu5fVMqnoXFasWDFqL9GyLDZv3kxubi5jx9oKCtFolNWrV4/I19XVRVdXF6+//jrnnXdetmcTiURYt25dVmjFYjFygXmZb0RAGRreipv20GFayBiShiYLMNM0NDTQ0NCAkVPFslm2YNyx9wBrN7xOor+bgb5uTGNIwcjr9bJ06VIkSWJedR6vlubR1dacNVtQFFsopdNpZEVlb3uU7liaa3+/ng8XHCDZ3z3i2iRJIi8vj1BuHi19CXJ9LjwnEi8wgxCCx7a08KWHt5E2LO7e1MP9t5x61PETqiv42pe/hITATCeJRqMMDAxgGAYulyur+Q4QCoX45je/mdWy/MPLdfz0mb2kDLuxGPKo3HhaJTcsrKYyzwfMZnl6GbeuupX1rev5bt13mRebx9TuqXxurq3fUBmqpDJUeULX9HZxrCml4zH4bIbfm+FMypvEa++3h7xdsuu4z+zJuieJpCP8adef+POuP7O0cinXXn4tC0oWIEtHTzNdeOGFI7YNw8gKSetdCP32rg+lvhEej4c77riDO+644x2o0cnx3cd3cagrRmnYww8vryW1v4/4lk6QoPCWGbjGhE7KMFVYArM3OUJYIgQbxu/nh3u/Qm/Knru6csKV3DrnVnI8OTDQzvrtEfZPv4hcxY8Sf4WolCQgPFhCYJj5uOijeEwrkYGDbH9+yDO+L5zD1CVnMX3JUvLvmgfC5MtbW3k6+itu6LHnKGclqjEicayATMoYUnf+YOh9FFWF8KpxEoaPzV/4Pf6tq7J/78udSMxfhipbnPY/Hyc00x5sbY428+wjzzE/Oo2F8VN5ZOUj2WPOPfdcFFni0lnlnF2ex0t/W0lXdy9T+waHUXo5PNA7FBRgWKdc0zTKy8spKSmhsbGRlpYWJEli9uzZnHHGGSdl/3T48GEee+wxOo9wEhBpj/Dni//MTc98FHe3LXjmqe3MrapgSknguD/g/v5+HnvsMerq6liwYEFWMJaUlDBr1qxsLzYcDtPS0kJTUxONjY2MGTMmW8bBgwdZt25ddluSJPLz8ykuLsYXzmdabXX2b4XjpvP46xbd8UEhJwhJSUrkAfKkOGNiQ+/ly2teob2pLrttCIk+4aXX8tE34OXi1n6mluUAcNMN16Np2og5OyFE1knBzYEQn7t/K0/vbGNrt4wlFdNvuYkID/93/UKa927hwgsv5Ocv1rHwNtsFoEuVyfVp5Hhd5Pg0cnwaXzx3MuOLbEWUu9Ye4g8vHyKaMoimjKyDjGVTivnfa2aN+hsbtPfFrREIBPj5uk5CXo0Cv0yioZuk3kFSt0joJmdNLuKcqfbweHmOl5RhMbU0xAdOG8Ols8rxHuGfNuAK8Ouzf803XvkGT9Y9yfq29Wzq2MS1k6+lxH9s/Yf/BNzKiQnbj8/6ODMKZ3Dv7ntZ27KWF5te5MWmFynzl3F+zfncVHvTqKHp+lP9/G3P31jftp4/LP/DMQXp282/hPLNvyNP72jlb681IUnwP++ZSUhVaP+7rawSWFiGu/rkDVElWULN96Lme2EK1PfX891Xv8tr2+xW2vic8Xzj1G8wp3jISbXRuJENiUqEsptY6CbO3z2F1wyFpSGBV4aU+RQk9rB/y5DfT4SgbOIUFlx2NePmLoCuAyBMTNXHEwN/YGy0BpfpocInM1eXQYKi903lOrmCe/50FwDdB9to++CNlHSXYCoaUutOkCR8c+cSPP886romwe4IkxdXEpo5NEz+111/JZj2cWnreWzsfB2wzW/OO++8IYNoU9D11y1s69sCEnhKPARTRcQbFQrKwsxdWoMv7M/2WgbTdDrNfffdR0tLC6qqcvXVV48YogdoamqivLw8e67hWJbFCy+8wNq1axFCoGkaVVVVVFdXU11dTVlZGYqi8Ntlf+SegheZagRY++JLbNq0iUgkwmWXXXbM57p9+3aefPJJkskkqqqOUBxzuVxHHRcKhZg8efIx67dgwQKKi4spKSmhsLBw1B7w0imlbPpmKf1xnUPdMVr6EkQSOgNJg0hSZ9YwZZ1AKMzrnmp6hY8O3U17WiNlDDVaf7BiD/fcZPdajzUsJ2Xm8AbnZX913Rxuf34fd7xoK3YO4vYPNRSDHhVVljAsQdqwaI+kaI8MaUx++uwh7eWkbmVdIQKossQtZ4zl8+dMOmGFn+d3d3DfhmNZmNrk+11ZwXjO1GIe/thC5lTlHLdhqykaP1z8Q2pCNbzW9hqfmfOZ/2iheDLIksySiiUsqVhCXX8d9+2+j8frHqcl1sIj+x/hE7M/kc0b1+P4NB+d8U7+vOvPPLD3AeKGbZ+9pnkNSyqWvOP1dwTjm6CtP8lXHrH1GG9ZMo6F4wroW3EIszeFkuMmtLz6nypfCMHD+x/mJ6/9hISRwKN4+OjMj/KBaR9Ak0eOrXe+8nAmgKwX5CCedIge/x7ucneyLDkNeWA/SSEIFxUz5fSzePXh+wBo2bebge6Mr5iegwAkg2OIHfoUk3QXIQXmuBUQEDq3Gneln/xn1jBpbxd7JxVwyOpm6uYt1CDwzp1D6H0fIbh8OVpxEZHuBA3/ZfdsapcOaRlH0hEe2fcIl7afy2a1HoBDahU3n7Z0hKCSFInXvHWk+g3yRIAbL/0ID/xsK8R0Tl86nfFzjx2V7amnnqKhoQGXy8W1115LdfXI57Bjxw4eeughZs2axcUXX3yUpqIkSXR1dSGEYMaMGZx33nnHtH0t8Af4zKKLAagoKuHhhx9m//79/OUvf6FgWCSERCLBihUr2L7dflfKysq44oorRuQ5GebOnXvSx4R9GrN8OSME4ZFcf/WlXH/EvrRhEcv00ErDJ+e8XJYlPr98EjcuriGpW7hUGZcqo2GRcVXPR5aM4+bTxxJLm/TF0/TFdfriOr3xNH0JnYrcoft+6awyTh2bR9CjEvRohL0anpP0Q7tsShE/vLyWNQc68agKbk3Bo8l4NAWPqjC/JjebV1Vk5o7JPU5pw65Vkrll5i3cMvOWk6rP/0+MDY/l66d+nc/P+zwvHX6JaDqa/Y5ZwuLyxy4n15PLvt596JY9KjUhdwI3Tr+RhWULj1f024YjGE8SyxJ87oEt9MV1asvDfO6ciaSbo0TX2HERcy4bj+x+874f+5J9fHvdt1nZuBKAU0pP4dunfZuKYMWIfCI5QMM9v+T5V6uADiSlANk6QF6ghT1Bu5W70lxDeWk1p7/vPYybdwqyrHDgtXV0NdYDUD5pil1Y1WlwwwrWbGtEbfExNuZlvl9FEuAa4yWx8QHavv53zO5uahWVhonvJUcEqL/uIpbddCvuklJIDUBGe3DHC/UIARXu7eS5KgG79f/wvodRkxLVyTL2KW106mNo8FdTluMlkUjw+uuv09HRwZw5c9jZZve+F6UnU/+zLSwE9uW4GDtrdKGybNkyuru7ueCCC7LafsMxTRNJktiyZQvpdJorrrgCsOczPB4PkiRx0UUXMXPmTKZMmXJCz2vKlCnccMMN3HvvvbS1tdHT04Ou6/T09HDPPfcQiUSQJIklS5awZMmS45oN/CthCzMXuf4Tm5M9Fjm+kcce6XlEkiQCbpWAW6XiOHKoLMd7TC3Xk0GSJK49pYprTznavtXhncGjeji3+twR+/b17qMl1kJLrAWAWYWzuKn2JpZULHlX/d46gvEk+cOaOl452I1XU/jfa2ahyRIdf98PFnhnFOCdfOyJ6xNhQ+sGvrrmq3TEO1BllU/P/jQfnPbBo8bYu9avZu39OzkcPwU9bQtQYXQgYi9QWfhe9mC7lBpbO4vrPngDWBYk+8CXRyAvPysY8yszHwlPCKoXsW1vPmONQ2BBp7Dwmwm6f/VZegIu9k2ayJjiIlqCZcyIz2WGEuD2gp1M8SSovPtqmvf04L7oW5QuXoI/dQCfnKK2YD3k2VrEQgieqHuCK3qXcbo+lSClfMpU+eqCquwPYOXKlViWRVOTPeQ1e9pMCl4PMyhLphR5kY+wD02lUlklg2AwyI033jjqD2rmzJm4XC4efPBBdu3aRTJpu6DKz8/n6quvBiAQCJywUBykoqKCm266ib/+9a94vV40TSMnJwdZlsnLy+OKK64YYZ/r4OBgMzlvMs9d9Rxrm9dSE65hdtHsfwlH8I5gPAl2NPfz02f2AvDNi6cyrjDAwJpm9MNRJI9CzsXj3lS5uqVzx+Y7uHPHnQgE1aFqbltyG9Pyp43IF+vs49XfP86exlJgEsI4hJUeNE1PEfZVsldpyebvTQs2r1nB7Bc/CHlj4RPrWfSe99O4fQtTFi8dEcgXYIz3Ba5LJ4gym75dzxJtWAFWmvaFZ9AQDOKbPJkO3cuTO2Xyih5na2A7izq30N1+Chv65jD2pW5KF8NM/sT0wheRTvscZIZIN2zYwJ2L/0Dfhm1ISPxN96ApJlfOtQWG1+ulqqqK+vp6enp68Pl8nHPhuRxONeDb0UXSEpRdP1JgWZbF7373O04//XRmzZoFvHF0hSlTpnDttdfyt7/9jbo6W+Gkv7+fSCTyhircxyMvL48bb7yR5557DrA1Aq+77jrC4fAJa8I6OPz/SIm/hCsnXvluV2MEjmA8CZ7Z2YZuCs6dVsw18ysx+pJEnq0HIHx+TdbQ/GRoiDTw5dVfZmf3TsDWOP3S/C/h04bmWPSUyeaH1rF5TRRDlCOsBF6eom+gPpunIB2ie0KIU/sraZJ7EZKJ0dTPyx39qLJMbV8jCEHJuAl85Fd34/YPaYQZT3yXrmf3MvX511EsiHuL0MJ+8m/8IDnvuZoX//EP6OhgwsSJyPVNLP7IHNb07GbDVoPNDZtZNKmCDYegqcWL2duKcnAlimTB7GsAaGxs5KmnnuLQa3tZZFTT6VNYGze4cFopBcMM3SsrK7NGwMuXL8fn81HznglsierkTMzBVzhyvu/gwYN0d3fzzDPPUFtbe8LDlOPHj+f666/n0UcfpbS0lPPPP/+YNn0ni8s1UoW9sLDwny7TwcHhnccRjCfB55dPYnJJiIWZWIh9jx5EpC1c1SH8821ttKZIEx9f+XEG0hHKPQWUaUHKJBflpqAsnaQsMUBZpBN3pJlHvRo/CnlJSIKQ7ObbE97HOZOvBsWeq7Mswd61jax/eBexpBshNALiRQaSu+lLJYfVTKZM6ackWUO5lUco52yunFGO/FwjbTGL3XljqNX3Qrwb/AX4c+wJHSsWo/uPf6Dnd/dgGTKqr5D0gqsJT6lg3McuRHK56O3tpaOjA0mSGD9+PE1NTcyuzGH/vkIuaLqA7mg3RRefg/fZbhJmmHV/WcNCC+TK+ZA/DiEEzz1v96JUSUFSZH6VtjXOrlswNN8jhKCxsTG7PahNqrlU5n90xjGfx6AbsRkzZpz03N2YMWP4zGc+c1LHODg4/P+BIxhPkgtnlAIQ39ZJck8PKBK5V0xAkiW6El185NkbORyzHV/3xHvYa8i4dBmXIaPpMi5DwqXLhNOlRFPgE2lqtQg/6G6m5OB34envguYjEZ7FM80foLm3GHDjE3uRxGq6+23jvcKqas6+6ROkHnyI5r8/Qt3sMKcnbZs3rSpEQUWQHsAnS7SpFaDvhb4G8BcgdJ2+hx6i845fYXZ1ATJqvsn9Z1dwQ3oWiuRDygz/DXqrqKqqynojApgRnMJ+9pPbl0NXTiWVrufZl1zK1l35yP73s3CGLczq6upoamzCkixyl5WwvaeK5x7bTnW+j1PHDgVb3rNnDw0NDdntgwcPMn36sRzN2fT19bF/v62gM2/evH/iiTo4ODiMxBGMbwIrrtP3D9vEIbi0Eq3Ix0B6gI8+dwtdPe1c8nohBf1uLOvEejGKXMbKsEyxJ0qJ1IJb8fHK4euJimJUBijgfhojUSzLQnW7WXj1dcw572IUM0FTUzMFsSSv5Exis1JPqVlM1bixaJkgvz4Zuix7SE/0NjCwo5vO228nnRmy1IrzKBx7gI0zqwgPzIY0JBhScNm7155THW4PuK99gI5XUviFmxiwaudGZhR0sM9WzGWSbw1M/86I3uLB4EEuCl3E/ImVPFQaIJLUszZoqVSKp556CoDy8nKam233UccTjJs2bbJD5FRXO0OWDg4ObymOYHwT9D9djxXVUQu9hM6sJGWm+NQLn2J/9z4u2lRMXp+H4T5QNLcHt9+P22cviteNoQEJnd76BpKxKG29Jm142cqgAs+DaO5CNK2X+qht3Dx2znzO/vDHCBUWEa0/SOR315HcbDvGtnw1bJcbaULnorIzuP+ph2h3NXOuPgtDLyTe4aL9S/9Hss72Iark5lLw8Y+TW9mGtOp7bNFCTEiWAxDNWGUnk8nsnN/EiROz19OX0Hmie4DFajHb1Ub279nPpZNdFLXvo6BYJf+Kb4Mvjz27d9PW2oYpmYRyPcwtnpt1LTacVatWEYlEyM3N5ayzzuK+++47rhKNYRi8/rrtHGD+/Pkn/wAdHBwcjoMjGE+S1KF+YhvaAMi9fAKmbPGlVV9iU/smluzIJa/Pg9utccV//Yjc0jJcXh+KOvptFkLQ395G64F9bHnuNVoP7EcYHYCOnmpBT0EgL5+zbriF8QtOywqMPav2sqbpTPxV/cw68AoJWUMGDlBITYGfp/v7GZCTxKUUs+rzaHilAOhA8nrJ/9AN5H34wyiBADxme1bdgcrpZg4AaqHd2xx0zKzrOgUFBVk7tKmlIXZi8gHTFoyiQ8D8JVwtPwfTLoNJ52NZFi+++CIAJQS5Ze8NRF9uIXzGSP+RbW1tvPqq7ez6ggsuoKamhi996UvH1eTcu3cvsViMQCBwTA8xDg4ODv8MjmA8CYRh0fuIPa/lX1CCqybEt9d9mxeaXmBqQ4Cxh211/ws/+3XKJp7YB1uSJHzhQuq2ddLTPgd3cA6zzq5gwgIXHXX7MdJpppx+Ju4jPLDs2xHDTG8nokDjvNnIyOBXWbhkLoos4ff56AISpMm38kGCnLNmU/Ct/0UrGuY5psc2WWhEkCd8IIG3zNZYLSoq4qMf/SipVGrEuQNuFavAi69LImR5iegJ9qmTmX7FkLf8WCyGKZnoks6yxGwAbl6zn0m9/Xzl/Mn43SqWZfHEE08ghGDq1KlMmGA7Angj84a8vDymT59OQUHBv43BvIODw78PjmA8CQZWNWF0JpADGuHzqvn55p/zyP5HKOl2s2Cnrel5+nuvo2b2iSuD9LXHWfHrbfS2xVFUmTOvn8ykU2wN16Ix1cc8Jp006O61NTv9QR/tc+ZDRwfnLzmHBQuqiDz3HGzaBIWFDJBC9gbQ/vwLSucvO7qwbjt8VUHTpXgkW8gEa0b6eT2Wl/5pFWH2dPUx1ipmi1zPzp07R8wJBoNBonOiSC/H8Cc8RHNdbOiN0L6/k+9cYttnvv766xw+fBiXy8V55x0dnqe/v/+Yzr9LS0tHhKxxcHBweCt5592W/xvjmZqPVhEg5+Jx/PXQffxh+x/wJxQu2JQPyEyaN4f5l19zwuU17uzmoR9vpLctjj/HzRVfnJMVisejpyWKZdqRH0JVNXR0dKDIChNUP0033kjzpz6Nq8eOxHHYHaPkirmMP5ZQBLj5RV49/W7SViWGEMQtQbjUTywWO6qnOJzpZWF2YjLBLGVCoARX7cheXspM8UTdE5wdtecAn8Yehr1mfhWybPskff755wE466yzRhjXm6bJb37zG26//XZ6enre8H44ODg4vJU4PcaTwFUWoOjjs3ii7gl+uvanKKbEe9fnYRkaRSX5nPvpryEswfon6oh02XaGkgRIINn/2UFTJTB1iwObOhACSsaGOe+W6fjDJxbSpftgCyIjGPsDuRBJUq0X0veTR0ltXYekaeTPnMH+ZJLiGUXMOafadgsXbQNfPqjDzhMupyV3PinfXp5sMwiFNCa6FV58aS3r16/n7LPPZuHCox35Ti8P8wtMPiB8VAyE+EfHGi6fejm6rrN+/XrmzJ3Dz6bdRvlWELLEXb0RZElwaqHBvffemzUDKSkpOUqBRlGUbBSHffv2ZeMRptNpXnzxRWbPnk1R0bEdiTs4ODj8sziC8SR5ueVlvvnKN0HAda/nYMV9eD0Kl37jp2huDxser2PTUw1vXFCGKYtKOeOaSSjaiXfeuw+1ZXuMri07cFVPZJJZhtn9Kt6lZ1L2ta+Q6O6Gxx6jo6efe9c3csXLF+CJNsHNL0D5yCgNBUV11E5YC90L8RXYijd79+7FNM1R4xhOKw+xGYMv5NSxu+RnFHQUIIRg48aNPP/882zfvp1rqs4lRjOH8xUKezq5wNfFYw9uzJYxYcIEzj///GPOE06cOJH6+voRgnHnzp2sW7eOPXv28OlPf/pfwqeig4PDfx6OYDwJtnRs4fOrPo8hDK46mIfcGUSWBJd84duECopoPdDHxhX1AMxaVok/x+6ZCQEIEPZ/mX2CvLIA1bX5J/2B7+qUEaYdGX3mtp0UFN+Aorj4Te0ifnD7zQCEdJ1wKIxojjOmbgvfL8nh+9Em6G0YEoy7HoOWLWxXk6wwnuDKayxunvtfdHV10d3djSzLjBt3bP+vIY/GK984B4/bZOE9/0uoLcRvfv8bBvpsBwQLFiyg7+V2tit1bBo4zGJNB90OKDxr1ixOOeWU44ZfmjhxIs8++yz19fUkk0k8Hk/W083cuXMdoejg4PC24QjGk+C1ttdImkmW9xQT2GcLvTOvuYaK2tmkEgbP3bULIWDSKSUsumrCG5T25pk0Uad+q4UkBAFfGarippc0A/OHeoLjxo3j1ltvpfFrL6Mg0R+vBbZD35DbNfasgG1/49D0JXyz6RbGxsYhT9azRv3V1dXHDEw7yGBIosn5kylqLqK9px0AxaPw1OanEAMCUzORgJhwce6Zi1i4YP4xYxweSUFBAXl5efT09HDw4EFyc3NpaWlBURRmz5598jfNwcHB4QRxBONJcPOMmymoP8Thp18njcSMuVOZeen7AVj9t70MdCcJFXhYcs3ENyjpzSEMg67f/hb5V79mscdNXzhMrHw6AWAHFlPLRw57SrJESpbwWZCTLsYElOGCMaORur6nn09Gp6NGFVDk7PzfcG83o6F3xPlI4xVsNPYQwwTATJqQ8YJTWFxKf7AagmUsW1p7Utc7adIk1q1bx759+7KBjKdOnYrf7z+pchwcHBxOBkcwngSp5p30PbSBtOWhrNjPWZ/7AZIksW9DG/vWtyPJEud8eBou77FvqzBNzL4+jO5uzJ5eJLcLz5QpyMfplQ2Sbmqi5UtfJrF5MwD1C05hV3ERlQkX5wLbgfNKjw6bZPk0iOoU6wV0agolwwVjz0FMwC/cqCiYEnQNRLPOvI8nGC3LYuOOfTz61FrC8XYSkpn9m45OT6iHL135JWrG1LzhtY3GxIkTWbduHdu3b88OnTp+UR0cHN5uHMF4Erz8ux/Tk/IQcAsu+fYvUVSNSFeCl+61hx7nXVBNcU2I/ieeJPH66xi9PZjdPRg9tiA0e3szE47D0DQ8kyfjnTkT76xZeGfNRCsvzwoCIQT9jz1G+/e+jxWL0VcyA89Yg7pcWwhWy5UgYAcmnysbKRjvueceWqxmzpNqKTJyafKpQ4Ix3gOJXlpUlcq0reE5oAvaD9cjhKCoqIicnJwR5em6TiQS4amnnsp6n3EBCQlcQiUaGmCgNI6rI81n+99DYbMbxrz5+11VVcXcuXPp6uqioaGBoqIiqqqcCOwODg5vL45gPAkWf+GXxH74GU655ib8eYVYpsXzd+0inTQpGRti3vlj6Lv/ftq+/Z3jlqPk5KDk5WH292N2d5Pcvp3k9u30/vWv9t8LCmxBOXMmyd27GHjqaQC8c+dSP+N6du1+jqTLg2RZPCX+yAP+y+iVp4+IbQi2gXzMihOTUuSbQQ6rKvMzcRnptp2gb3HnMy5eDUAMweQp4zE5F4/HgxCCrq4uDhw4wIEDB2hoaMAwjGz5Ho+HQ0YOC+MFLLUKUeYVcUH9e/lx5FZ8CRdb67pJ5iqcPqEQl3ryJrOKonDxxRezfv16Ojs7mTdvnqN04+Dg8LbjCMaTwBMMcemP7spub3q6gdaD/WgehXM+PA29/hDtt/0YgPCll+CZNh0lLw81Pw8lLx81LxclNxcp4ztVCIHe3EJiyxYSW7eS2LqV5O7dmF1dRFeuJLpypX0iVaXwk58k/+abWPu1Z9DVDiBAUE9TvfR8yscvxSdVHFXfQCBAR0cHCdLkiDxWB8Zz+YwzwExn5xcPSWHGJOxemOFWcXvchMNhDhw4wKpVq+jv7x9RpqZpTJs2jenTp1NTU8On799Kw7YeFGQad+2jSM2jNj4BJPj2wTb272jkzx9ewJKJbz4CximnnMLcuXMRR/a2HRwcHN4GHMH4Jmmr6+e1J+sBOON9kwiEFOpv+gIimcS/aBGlP/oRknz8XpIkSbgqynFVlBO+6EIArFSK5M5dWUEpkkkKPvFxvLW1GLpJb08cy2PbGo479VQuPe+yUcsPBGyfp1EpTdBSUGZcAAu/Yv8x4yM1v6+SkvREGtUuNrmbef7Hz48QQIqiMGbMGMaPH091dTUbNmzgwgsvRNM0AGrLw7y4rQNw42oTnB1cAEC81Mf+lggFAVc2sPM/g3ocR+wODg4ObyXO1+ZNkE4YPHfnToQlmDC/mIkLiun4yU9J7dmDkptL6Y9++IZCcTRktxvfnNn45hxtktDbGscyezA9trlDbo+bgZea8M0tRgkc7Xh7UHuzLV+icHwV3zh16dAfl36FLQUX8Pf713KK5zVicpzBWFnhcJjJkyczfvx4xowZk3Xqrev6UUOZteVh7shoo5ak8rlcPxuAVZpd2IW1paiK43nQwcHh3wdHML4JXr5/H5GuJME8D2e8byKxV16h5y57iLX0B98fGb3iLaTrcBTL7MLKCMbAfpP+vfU8lkxw9dnjcKsjPcgM9hjzxng49ZJxtlu4SBuJZIqNe5tY88qrTNdixADJUijKL6W99zC5ubmcf/75J1Sn6WVhBoBDmNSgELB84JL5TYvtgOCSWeVv2fU7ODg4vBM4gvEk2b+xnT2vtiFJsOxDU1FSURq/8lUAct53DcGzznrbzt3d0I1l9eA5fJgyJY+8wgBdUpLb1h7i2nOOdigwKBhjsRhPbmvFveanGB37eV2eQ9qye35pLY3PLCW/dwpl0w3aew8fpY16PMI+jdryML91KfwwmIO8rYvucj89h/qoyPUyp+rEy3JwcHD4V8ARjCfBQE+SVffYphlzz6+mdHyYw5/8FEZnJ65x4yj+0pfe1vN3tcTB7EZNR6k1q1ALFXZKUaaU5SHLR2trhkIhwuEwVm+a5oceokUOAnPBgiJ3mtJynW9Zz1AeLGfFFZ/kH//4B8BJCUaAxz+1GID04QFiPo072zK9xZlljhapg4PDvx2OYDwJNj/XSDphUFQdYt6F1fTd/4CtOapplP/3T5G93rf1/GfdUEtXg49IGwSCtibpdktl6jEM+8E2pygrK2P37t2ZAGMSYfkwF1kbGJ9q4P5oFe9NnMe5zYuJb2ynr68POHnBOIirIohSFmDjr9oAuNQZRnVwcPg3xBGMJ8Giq8bj8WtMXFCM0VBP+223AVD02c/imTLlbT9/KN/LjGuuo/N738Mqt4XODuD6Iwz7Dx8+zOrVq7Ou3QBqzCLCVpD+/D8wIWI7+l6ru1icrKJkIA8rbf7TgjGS1Al5NP7xycUc7IwyrjDwpspxcHBweDdxBONJoCgyCy6qwUqnqf9oxjRj4ULybvjgO3J+0fQaL656gcQpp1Ca0vAIwQFMppbaPlLr6+tZvXo1dXW2KYYkSUyfPp2yWCWVu1J0EGOFNvTID2ka18ftnuf+/b1Zm8WTFYxJ3eTc/11NQ3ecrd9cTtinOULRwcHh3xZHML4JOv/3/0jt2o2Sk3NC9opvBXVbOml66EESqg/Jssg1vbSqcXRAjXVw550PZ32cyrLMzJkzWbx4Mc888wxrWlazXJpK2PJRp3iBXgC6NJMi0xaqnYkYlmUhyzKh0LGHZkfDoylYGdvHDfU9nDO1+C27bgcHB4d3GkcwniSxV16h5847gYxpRvE7E0n+4MZWNndpUAL+gQGebbqVv02/mYtUifvvs+MUDoZkWrRoEbm5uUDGLVwySlxKkS8F6ZHDQAtxSSJEwHYeLgThslyuW3Yd8Xg8G8niZKgtD9PUk+DmP2/kc+dM5NNnv31htxwcHBzeThzBeBIYvb20DJpmvPe9BM8++x07d3dTL4YWB8IEvS5CS97LsoEBdF1HURTmzZvHokWLjurtBQIB2tvbiYg0AIqVjwX0KjJVUTs8VtSC3JIQEyaUvOn6TS8Ps2K7rXST49PedDkODg4O7zaOYDwJun7xS4yODlw1NRR/5cvv2HlNw6KnI43pt00f4gXFtPZEAKisrOSSSy6hsPDYvkgHbRn7SNFvCirVMfSpGyjSTQrbrwAgagqqCv45jdrJJcHs+gW1pf9UWQ4ODg7vJo5gPAkKP/c5rHSKvGuvfdtNM4bT2xZHN/owfbaLt/5IBE1WOfuss1mw8JTjDn1m/aWWakRryrntrP+jM3Eb/6+9+46yqr7XP/4+fXrvjd47EpGioCIqXlBTMBYENZaILsu9JiYWNGosSYyJP8uNxnajYomgUUSKIlIUQQbpZWYoA0xvZ/op+/fHZCYHgeFMPTPD81pr1uLss8/en/kA88xu3+9//XERk01mnG6DCo9BkfMwhd8fICMjo1V3pU7uH8/MUSkMTgo/bpYPEZHuRMHYApawUFIefbTT97t3Rw7l8bvA3DDkW6onmrNqB7MwL5QzjH8/ongSjcEYmWJj/Mx+DQvrXJw9bjQ43Xz+TTlWu5kjW74lJyeHyy+/vFXBaLeaefbK48d3FRHpbhSMXZjL5eLLL79k7bq1GHYD3G5SSmu4KPw8sszVfLYrn8csI5vdRuNA4pWVldTUe3h+1T6yC6sojHiC4JpohiRcTWhICEfa+AyjiEhPoWDsogoLC3nvvfcoKCgAwFbpwn5kO6ONoZjCTWQbBv0TTv2sYEREBFFRUQSZHBz+01rGVRfyt9glhLj34jbcPP6rBSSFJPHoo0sABaOIiOYD6oIyMzP529/+RkFBAaGhoVwx+wrG9k0l1Gwm3NHweEi2YWVgYvgptgS9e/fmzjvvZGDYOILKTfRyxWEP30bfmlTe3/MnbO+X4HQ6m55hDA8/9TZFRHoyHTF2IfX19SxZsoTMzEwA+vTpw49//GPCwsLYtn0bkTEJWN1xAGTj5WI/grFRUHwIXsPAajITa9hIq08k1BsM9d6moeAiIyNb9QyjiEhPomDsIgoKCnjvvfcoLCzEZDIxdepUzj77bMxmM2U7vmT79u2YohKYWNvwKEQWHgb4cSq1UWhMEDVeCLVAkiuOtLqG0WkOHHJSm1UD6DSqiAgoGDtEWVkZbrebmJgYv47AMjMz+eSTT3C5XISFhfGTn/yEPn36ALBnQx6bPskEE0RWVGJxl1EVFk0+hl+nUgHefvttjhw+wlgGMYBIEl2xpNU3BGNhhQtzjYJRRKSRgrEd1dfXs3LlSr755hugYdqn1NRUUlNTSUtLIzU1teku0cb1fU+d9u3bt+nUaaNDu0o5UF4BUWCprqDw6/u5/erfEudKIibU7lddTqcTZ6WTcuoASK6LJ72u4ciz0mtw/lln0GdAxjG1iYicrhSM7eTgwYMsXryYkpISoGHc0traWrKyssjKympaLzo6mtTUVFJSUti8efMJT536Ks514rbXAVai46IIue5qVt9xJW6P/xMANz2yYWoYFi69LoWU+oZrlUaojZjYaGJio9vy7YuI9BgKxjZyuVx88cUXrF+/HsMwCA8P59JLL6VPnz7k5+eTm5vL4cOHyc3Npbi4mNLSUkpLS9m2bRvAcadOfXk9XoqPVOKJbgjB/lPPZ8j0iwGwtuAemcYj0Fq7m/I6gwhTJHZseAwDW3xIGzsgItKzKBjb4PDhwyxevJjCwkIARo0axUUXXUTwv4eLS0lJISUlpWn9mpoaDh8+3PQVGhrK+eeff8ypU19lBTXUe6sxLBYwvKRsjaIgewvRPxnQokBr3L4tzU5VWjqTB4yg+MN9lOdXEx4fxOrVq4mKimLYsGFYLJbWtkNEpEdQMLaC2+1m9erVfPXVVxiGQWhoKDNnzmTw4MHNfi44OJj+/fvTv39/v/ZTfLgSj6kEvB4s9XWYC93UF1Zw6z+3cP/PRtIr1r9rgo3BGBxj4sz/ajgy/S4xlF3ZTkZGGKz8/HPMZjPDhw/3a3siIj2ZgrGFjh49yuLFi8nPzwdg+PDhzJgxg5CQ9j8lWZxbibWmjrDdm0myxEMGlBv1LN9fwRMO///qmgYSr6wEwDAMQsLtRCeFYAl1A3qGUUSkkYKxBTZs2MDSpUvxer2EhIRwySWXMGzYsA7bX221G8NbjAlIMaIA2IdBbKid2BbMYBEeHk50dDRhoWEcfXkrnn1ljLlhOBN+3J8tW7YAelRDRKSRDhFaIDo6Gq/Xy+DBg7n11ls7NBQBpl41iPPn/oiI5ARiLLEAZJnwa4xUXxkZGdxxxx2M7nsOh3c23DVb9PdtuPKqmka9UTCKiDTQEWMLDBgwgBtuuIG0tDRMJv8fl2itsrIyvti2AyM+kciKhscrsvD6/WD/D4VFOSjy/ue1JcJOaWkpoGAUEWmkYGyh9PT0TtvXkZ3fUlxcjMPlwhKRBkAOXs5KbNkRY6PQKAe+eb7ird2UWcsABaOISCMFYxe1Y80RPv9sH1ggpLwIb9Uhqm02ciwe+ie0/Ijx7bff5ujRoyQa/UgnhlqvgdvlpayyDFAwiog0UjB2UXk55ZTXl0EwGNUuatb/ldd+ehvBjoEMbMURY2VlJRUVFdhsbr6pclPmNhgcH8zVU66mtLSUxMTE9v8mRES6IQVjF1V0qHEoOEiICSXqv+/gsZ9ewZPRrRu6rfGRDWuol7wKA4CIuGDi4+OJj49vn6JFRHoABWMX5PV4KcwvwYgGDC+9hw4n+fpb2rTNxmA0B3lovP8mIj64bYWKiPRAelyjCyorqKGOCgDMdbWkFo3myGNfU7uvrNXbbAzG0Lj//JVXuUtYvXo12dnZbapXRKQnUTB2QcW5lRgYmGtrsNRU4impx+t0Me+d71ixI79V22ycYcNkbxjpBhPkF+fy+eefNw1oLiIiCsYuqSi3Enu1jdCc7UQXlGDHgdfwstFZQ2gLhoLz1TQsXFUlGcNiSR0YRXlFOaA7UkVEfOkaYxdktZtxBJdTXwFx1oabbQrc1bhtMKCVzzBGREQQHR1NdEwUM2ePAuC1174HFIwiIr4UjF3QmAvTSR00iY8/3E7adzYA9hseYkIdxLVgjFRfaWlp3HHHHccs03BwIiLHUzB2QXv37uWf//wnxISSTMMR4naLtcVjpDbH4/FQUdFwg4+CUUTkP3SNsYvxerwcydmD2+2mrjQPc3jDRMd7LKZWPdh/Mk6nE6/Xi9lsPulEySIipyMdMXYx33+Ry4a1OWADV/lRPEW7qLHCvrBoprdiKDhf77zzDocPH+anP/0pXm/D04xRUVGah1FExIeCsYspPOSk3uwEIDbfTu2299kfdRGpQ3/K0JSINm27cVg4p9PJoEGDuPXWW6mrq2uPskVEegwFYxeTl1uEYfGAYZASbiPt2b/SJymZGSOGt3nbjadMq6qqsFqtJCQktHmbIiI9jYKxC/G4vRSVFEAkmOtqSM0YRNg552F2WNpl+03PMlZWtsv2RER6Il1c6kJK86pxWRpOo5prq0lnAkcWrKNs/ZF22b5vMK5bt46vvvqqaaJiERFpoCPGLqQ414nFHYrNWY6lqoK6wioclhB+8eH3THA6+e/pg9q0/cZh4SorK8nOzqasrIxevXoR3coZO0REeiIdMXYhRbmVOKrDCcrdS5CzgnBzLABZeIkNtbd5+41HjBUVFZSXazg4EZETUTB2IVGJIcSm1gMQZo/CZDJT766hFIOBiW17VAP+Myyc2WzGMAw9wygicgIKxi4kY3QEk+eMIHL6OBKsDUFYUNdwzXFAOwRjSkoKd9xxB9OnTwf0DKOIyIkE9Kfi6tWrmTlzJikpKZhMJhYvXnzM+/PmzcNkMh3zddFFFwWm2E7w7bff8vc3/o/DmEjzNJxGzTZcRIXYiAtr+6nURhojVUTk5AIajFVVVYwaNYrnnnvupOtcdNFFHD16tOnr7bff7sQKO09tlYvDBw8BUFi7myh7OgBbLVYGJoRjMpnabV8KRhGRkwvoXakXX3wxF198cbPrOBwOkpKSOqmiwNn+1WGys3LBAhWlR/EcXE99ZSHr04czqR3HSH333XfZsWMHoGAUETmRLv+4xqpVq0hISCA6OprzzjuPRx99lNjY2JOuX1dXd8wwZ40zSLhcLlwuV7P7anz/VOt1hMM5BRgWFxgGI7eFUXdwLW73IUbPms6ZvaLarabGh/snT57MsGHDWrTdQPanO1B/mqf+NE/9aV5b+9OSz5kMwzBatZd2ZjKZWLRoEZdddlnTsoULFxISEkKfPn3Iysrit7/9LWFhYaxfvx6L5cSjwTz00EM8/PDDxy1/6623CAkJ6ajy22z/2hpKQ3Zhrq0mKW8n6Wddgrm2jto+vdt1Pzk5OZSVlZGamqoh4UTktFFdXc1VV11FeXk5ERHNjzvdpYPxh7Kzs+nXrx8rVqzg/PPPP+E6JzpiTE9Pp6io6JTNcLlcLF++nAsuuACbzdaq76M13PUenl3wFtVhB7GWFTE+LIxJN92LNT4Yk7V9LwMvW7aMb7/9lokTJ3Luuee26LOB6k93of40T/1pnvrTvLb2p6Kigri4OL+CscufSvXVt29f4uLi2Ldv30mD0eFw4HAcP8u9zWbzu5ktWbc9lB6pwW1tOMVpqa0mI24aJc9vxXVOCukX9sFqab9wbLyJZ926dU2PbbRUZ/enu1F/mqf+NE/9aV5r+9OSz3Srh9hyc3MpLi4mOTk50KW0q6LcSoJqkgkqLcZSXYG3tOEg/r4vdjD31Q3tuq/a2tp23Z6ISE8T0GCsrKwkMzOTzMxMoOH6V2ZmJgcPHqSyspJ77rmHr7/+mv3797Ny5UouvfRS+vfvz4UXXhjIsttdUW4l9too7Hn7cdR7CbfFAbAXL/3i23dkmokTJwIwYsSIdt2uiEhPEdBTqRs3bjzmOtfdd98NwNy5c3nhhRf4/vvvef311ykrKyMlJYXp06fzyCOPnPBUaXeWNiiaytIj7PzCIDyo4Y5bd10FhQ4rg5PaNjnxDyUlJXHPPfcQHBzcrtsVEekpAhqMU6dOpbl7fz777LNOrCZwrDE19JuSSEHi+ThWHgCgorYEHDEMSmr7UHA/1DjLhoiIHK9b3XzTU61Zs4Y9e/ZgHWrlbKPhNOqh+mqgY4JRREROrlvdfNMTOUtqyT10GIDcsrUkmBqGgttj8pIeE0yYQ7+7iIh0Jv3UDbDML7OorqkCw6CkqJSq3R9iD+rFN6nDGJTYvtcXRUTk1BSMAZZ7sOFo0Vxfy/hdcdiO7Kbes5NJN88lIy0+wNWJiJx+FIwBVlicD2Yw11QRGmRmxIYN1B88yO1DhgS6NBGR05KuMQZQfa2bSlcp0DDiTd+EodRm12BL7RfgykRETl8KxgAqOVLVNBScubaalOCxlLy5k8yVORwuqwlwdSIipycFYwAVHnISUTqM8MICLLXV2KobHrp/8fON3PTGxgBXJyJyetI1xgAqOujE6grD5DyC2WQn1BoFQHFdebuPeCMiIv7REWMADTsnlTMuisftcRNhb7gD1VtVyJ6weAbrwX4RkYBQMAbQnkPfY0uvI/WXP6E+uWES5brKPCrtIRrxRkQkQHQqNUA8Hg+rV6+mvr6ekHNCGOFNBDMU1pUDMDhZwSgiEgg6YgyQzHV7qK+vx46brJy3SfWkApDjqScm1E58WM+aQUREpLvQEWOAbF63HQBHbTXu/UHsyvk7Y8pS2JzQn8FJ4ZhMpgBXKCJyelIwBoDhNSguzwc7uJwVDC2KJcFdg/vIJi64/kosY/sEukQRkdOWgjEAio9UUmdpuJZoqq7EaoZzFi/DcFYyKDgYs90e4ApFRE5fusYYANk7cvFa6sEAS001Q+JHUbn6CN5aq0JRRCTAFIwBkJOVCwYEu+sxGV4SQ0dQsXQ/K1dksy6rKNDliYic1hSMAeDJDye2YCLx+bsACPFEAvDlys+5b9G2QJYmInLa0zXGTlZb5aI0rxqTYaG61k2QJZRgcxiG4aW+Mp9BiXp+UUQkkHTE2MmCQm3MfXwi512TRr0HIhz/HgquMp+csDiNeCMiEmAKxk62d+9e3l30FhW2UgY/fBP5/Rtm1PCW53IwPJEhGvFGRCSgdCq1k+3fv5+DBw8SF+4g21pAn/pkAGqdRylKHcggzaohIhJQOmLsRF6vwZYNDTfcpG57ga3f/Y3edQ3BeNRdSZDdQkZMSCBLFBE57emIsRMVHCynsr4ETPB9bij2aguflD7JFVsT2BmdxqDEcCxmDQUnIhJICsZOtOv7LDAZWAwzxZU2hrvsjOo3Er7fwNnTZjFuxpBAlygictpTMHainOz9AIS5a/ECqanxTF7wLMaDBng8mKz66xARCTRdY+xEhaV5AARV5QMwIHYapf/ciyu3UqEoItJFtCkY6+rq2quOHq+msh5PrQmT14rHWQyAuSqGqm/z+Mfyvbz5zQG8XiPAVYqISIuC8dNPP2Xu3Ln07dsXm81GSEgIERERTJkyhccee4wjR450VJ3dXn52BRHlg+ldM5zaSjdWs53wmlAAyha/xp+X78WsG29ERALOr2BctGgRAwcO5Prrr8dqtfLrX/+aDz74gM8++4yXX36ZKVOmsGLFCvr27cstt9xCYWFhR9fd7VSV12G1mQmPKscEBEclAeB15hFUXaQH+0VEugi/Lmw99dRT/PnPf+biiy/GbD4+S2fPng3A4cOHefbZZ/nHP/7BXXfd1b6VdnN9x8UweOI57PhyFfsPBBMRlgKAu2QfORHJGiNVRKSL8CsY169f79fGUlNTeeKJJ9pUUE/14osvYhgGV199NdlXJDJ0TQrUgKc4i93pI5ijMVJFRLqENt+VWlVVRUVFRXvU0mOVlZVRXl6O0+kkqnATO/O3MbCmNwDu4n3si0pjSLKGghMR6QpaHYw7duxg3LhxhIeHEx0dzYgRI9i4cWN71tZjrFu2BYBIWxD5i67BXAXlFOOtreCg2aDWHkT/hLAAVykiItCGYLz55pu57bbbqKyspLi4mB//+MfMnTu3PWvrMQ4eOgiArdrJBzvPJD3by4bq56lafh97otLpHRdKkM0S4CpFRARaEIyXXnophw8fbnpdWFjIrFmzCAkJISoqihkzZpCfn98hRXZ3JRUFAIRUHcHjsTInaRIXjr2CoCEDuPzaGbx4zRkBrlBERBr5PdzKNddcw3nnncf8+fO5/fbbue222xg2bBhTpkzB5XLx+eef89///d8dWWu3VFpYQT1OALxlhwEbaSMm0fu8/4J5twS2OBEROY7fR4w/+9nP2LBhAzt27OCss85i0qRJLFu2jEmTJnH22WezbNky7r///o6stVvavnkPmMBmBFFebSXcFkvwF1EUvb4dw9BINyIiXU2LBuiMjIzkxRdfZM2aNcydO5cLLriARx55hJAQzSF4MtVFBsFVqUQFl1KJieiwFHAZ1DrruPKlrxnfJ5a7LhgY6DJFROTfWnTzTUlJCZs2bWLEiBFs2rSJiIgIxowZw5IlSzqqvm6v6qiFMGc/UmtyAAgLa5iYuO7z9+j96bus2l0QyPJEROQH/A7Gt956i7S0NC655BJ69erFp59+yoIFC/jwww956qmnmD17tm6+OYHYtDAiE4KpDYkEINmaAYCnaA/FQRGM7RUdyPJEROQH/A7G3/zmN7zyyivk5eWxcuVKHnjgAQAGDx7MqlWruOCCC5gwYUKHFdodlZSUkH6mjSseGEevcWdSkxhMHLEYhhdPSQ67ozM4Q8EoItKl+B2MlZWVDBo0CIB+/fpRXV19zPs33ngjX3/9dftW181lZmby+uuv869//Ys+087h8IBgALwVh6kxPOSGJygYRUS6GL9vvpk7dy6XXHIJU6dOZePGjcyZM+e4dRISEtq1uO5u754sAHrby9i69g8Mq+4HgKd4H3uj0kiODiU5MjiQJYqIyA/4HYxPP/005557Lrt27WLevHlMnz69I+vq9mpqajl69AiYwLH7S7YYqzkQNI1yVzb2wp3sjs7Q9UURkS6oRY9rzJw5k5kzZ3ZULT3Kto27wWRg8QaxdftBPFXDKBm/n7rd2ZiPHiF30jwmZkQFukwREfkBv64xLly40O8NHjp0iLVr17a6oJ5i9859AEQHxZBfHQTA0z/9G72umEfEjBk8+7s5XDk+I5AliojICfgVjC+88AJDhgzhqaeeYufOnce9X15ezpIlS7jqqqsYO3YsxcXF7V5od3OkIBeABHstbsNCbFAMkRHJxM6ZQ+rTf8KRmoLDqoHDRUS6Gr+C8csvv+TJJ59k+fLlDB8+nIiICAYMGMCIESNIS0sjNjaW66+/noyMDLZt28asWbM6uu4urbqqmmpXKQAxdQ1HjuMSLyLviW8p/zYvkKWJiMgp+H2NcdasWcyaNYuioiLWrFnDgQMHqKmpIS4ujjFjxjBmzBjM5jbPe9wjOAtdRBWPgZBqXHWvY8JGiLnhjt031m7hnRXb+PWMocwclRLgSkVE5IdadPMNQFxcHJdddlkHlNJzHN1Xjs0dTq/0DI5+6yLKnoodB153DdNevIvVZ11P2OUjA12miIicgA7xOkD6kBjGz+pDn1HhlNYFExeUBoC3aB9gsCc6nTG6I1VEpEtSMLaz6upqVm9YjjWlnH5nJNP/yv/CFpsIgKd4L3kh0cSnJxEVYg9wpSIiciIKxnZ24MABtmzZwpo1awgKDaWgj4VUy78HDi/Z1zA+aoYe7BcR6aoUjO1s6+ZdAKSnpMHfp3Ng82fEeCIxDDee0v3sjUrX+KgiIl2YgrGd5WQ3zLtIfh2bthxid+kRnkp5ldoDy8DrZnd0uoaCExHpwvwOxqFDh1JSUtL0+tZbb6WoqKjpdUFBASEhIe1bXTfjdDqpcVcAEO/exar8fpz1fRSWZC/uzMV4TWaiR4+gb1xogCsVEZGT8TsYd+3ahdvtbnr9j3/8g4qKiqbXhmFQW1vbvtV1M9sydwNgdYfiOpoJwLDe6fzpv14g7bn/R/K9v+L/bjsXs9kUwCpFRKQ5LX6OsZFhGMctM5lO7x/4e3b8e3zUkESO7CvDYY6nX9yFuIvchJ9/foCrExERf+gaYzsqKy0HID0ujLyqYBKDexF8KJ7SRXvZdKCUWpcnwBWKiMip+B2MJpPpuCPC0/0I0ZdhGISXDCO24CxSjH14MRMTmg5AVek+nljwd8Y9tJQ6t8JRRKQr8/tUqmEYnH/++VitDR+pqalh5syZ2O0ND6r7Xn88HZUX1lBTUY/NGkRdVcNA4QnBvQBwf/4eDxTu5MGb/p9m1BAR6eL8DsYFCxYc8/rSSy89bp2f/OQnba+om4qIC2LOYxMozavm28UrCbFGEG2OwYsXT/FesiJTGd03PtBliojIKbQ6GOVYzz//PGFhYcyaNYtL/+c+XnruCSgAl1EE7jr2ROn5RRGR7sDva4y1tbV89NFHOJ3O496rqKjgo48+oq6url2L6y5KS0spKiri4MGDhFrBbfYSVhcOgKlsP0DDUHAKRhGRLs/vYPzf//1f/vKXvxAeHn7cexEREfz1r3/lpZdeatfiuoudW/cAEBUWh2PlfWz7fyMZWNNwfdG1+2sASjL6kxoVHLAaRUTEP34H45tvvsmdd9550vfvvPNO3njjjfaoqdvZvbPh+UVLdQRfrt7O+v2x/CrhCZalfYK3YDdl9lBSRwwKcJUiIuIPv4Nx7969jBo16qTvjxw5kr1797ZLUd2JYRgcLcgFICM5nm35wbjy4vnloLmM9gSB14Vn2Eh+ekZ6gCsVERF/+H3zjdvtprCwkIyMjBO+X1hYeFo+slFYWEi9pwYME2nWXPZ4bFjNBrPP/SXm883Uz7iaPl4vQYMSAl2qiIj4we8jxmHDhrFixYqTvr9s2TKGDRvWLkV1J1u+2wqAvT4aT8E6AC5Iv5aKj/dj1HpwDBhA0CCdRhUR6S78Dsbrr7+eRx55hI8//vi49/71r3/x2GOPcf3117drcd2Bx+nAVhdNQkQGeQcOEmmLI8KcQuWmPFbvL+H9TbnkV5zeg6uLiHQnfp9Kvemmm1i9ejWzZs1i8ODBDPr3UdCuXbvYs2cPs2fP5qabbuqwQruq+qNhRJWOYOykZL7dBskhDXejllvyKH7gVd4LG0TpDT/lxnP6BrhSERHxR4sGEf/HP/7BwoULGTBgAHv27GH37t0MGjSIt99+m7fffrujauyyDMPAYjVjtpiIS6ql0u0gIbjhGqy14hBDt69jcOlBJvSLDXClIiLirxbPrjF79mw+/PBDtm/fzo4dO1i8eDGzZ89u1c5Xr17NzJkzSUlJwWQysXjx4mPeNwyDBx98kOTkZIKDg5k2bVqXuvN1x44dTL66Fzf86WxMIVaCo2OIC2q4+9TYuxmAnKR+DEmOCGSZIiLSAn4Ho9fr5cknn2TSpEn86Ec/4t5776WmpqZNO6+qqmLUqFE899xzJ3z/qaee4q9//Ssvvvgi33zzDaGhoVx44YVdYkJkp9PJe++9x5///Gfq3bWkDx3BkOuuJsgcTK25DvZtwouJiHFjsWhiYhGRbsPva4yPPfYYDz30ENOmTSM4OJi//OUvFBQU8Morr7R65xdffDEXX3zxCd8zDINnnnmG+++/v2nA8jfeeIPExEQWL17Mz3/+81bvtz3s2rkLgNTUVMLsZti7nIrdR4D+OO2FBBle9kckM3boiR9vERGRrsnvYHzjjTd4/vnnufnmmwFYsWIFl1xyCS+//DJmc/vPd5yTk0NeXh7Tpk1rWhYZGcn48eNZv379SYOxrq7umDFbKyoqAHC5XLhcrmb32fj+qdYD2LzpewCMkkjqs9ZgW/gzDkRcS4otkqCaowDsiO3NRb0i/dped9CS/pyO1J/mqT/NU3+a19b+tORzfgfjwYMHmTFjRtPradOmYTKZOHLkCGlpaS2r0A95eQ1zGiYmJh6zPDExsem9E3n88cd5+OGHj1u+bNkyQkJC/Nr38uXLm33f7XZzJL9htBvq7Kx59xV27j2LLRmZvDRkFf/7egLRwL74Puzb9BXZPexM6qn6c7pTf5qn/jRP/Wlea/tTXV3t97otGvkmKCjomGU2m63L/Xbzm9/8hrvvvrvpdUVFBenp6UyfPp2IiOZvgnG5XCxfvpwLLrgAm8120vW+//57tm7disUVwtTLxnPw3Zeo9diYHTmKm8+7hriP/0RdXh43/PJyxozrOQ/3+9uf05X60zz1p3nqT/Pa2p/Gs4f+8DsYDcNg3rx5OByOpmW1tbXccssthIaGNi374IMP/N55c5KSkgDIz88nOTm5aXl+fj6jR48+6eccDscxNTay2Wx+N/NU627fuhOAIFccvYdGsb6gBoc5jj5jpjAw/Sx47z08lVVYwkJPuo3urCW9PB2pP81Tf5qn/jSvtf1pyWf8Dsa5c+cet+yaa67xe0ct1adPH5KSkli5cmVTEFZUVPDNN9/wy1/+ssP2eyput5uDhw4CkJHcD0/OKopqg5maNIuQdSnU9C4heHBMjw1FEZGezu9gfPXVV9t955WVlezbt6/pdU5ODpmZmcTExJCRkcGdd97Jo48+yoABA+jTpw8PPPAAKSkpXHbZZe1ei7+sVisD7eeRe+QAQ8/tx/4vH8VsshIXlAr1Xr6vKOWZv+3hwmGJzJvUJ2B1iohI6/gdjB1h48aNnHvuuU2vG68Nzp07l9dee41f/epXVFVVcdNNN1FWVsbkyZNZunTpcdc6O1N1RT2F+6txGPH0GRHLl+/uJs4xBIvJSom1gqBb/4ef2GP4Zt49oGAUEel2AhqMU6dOxTCMk75vMpn43e9+x+9+97tOrKp5tiALF1w/lOLcKkIi7eyvjqd/UMP4qMUh+SRVVtDHWkft8N6BLVRERFql/R9A7MH27dvHq6/9nQpTLhMu74fbVc+YGZcSH9kbAJu7BICdMb04q7/mXxQR6Y4UjC2wY8cOjhw50vQcpT0omPEzryDG3PCspfVAFgD7kwfQL14334iIdEcBPZXanXi9XnZsb3hMIz2pD1QchY/vItd+MVb6csReiH17w6TF1lGjMZl62FP9IiKnCQWjnw4ePEhtXQ0mr5XSPWaczg/I27iBb/tVcCBuBGmOGMaVFeMxmelz9pmBLldERFpJp1L9tHNnw9GivS6WvqMT2LtmBR8dHkrRHgfLMjaQYG8YVSEnOo3xQ1IDWaqIiLSBjhj9YBgG27ftACDUk0BqLxvf5BQBkUyccBG3/exGnOvW4rzAyZmDBpEQ69+YrCIi0vUoGP1w5MgRKquc4DXTf0B/3HuWkVsVzoCIsWQkTwWXQeSks4mcdHagSxURkTbSqVQ/mM1mwknCURtPv9GJHPjqI0wmOyNjplD/aQHu/GqKK+tOvSEREenyFIx+CLVFEZQ3kMjKwfQaEkn2ziySgvtgNdkpspVRUHeYWfe9y5SnPqe8umvNNiIiIi2jYPRDydEqrA4Lyf0isRulZFdGkR7aMJ3U1rgs8t74gJc/+z0/W/cukSEaFV9EpDvTNUY/9B4Rxw1/nNwwTmrxYepcVlJC+gNgHRZF9TNrCQXsg3rO3IsiIqcrHTH6yWqzEBEbTGLf/lwx/3FsZgeF1lLGDD+DyP17AEiepOcXRUS6OwVjS1SXQFUxpXsLAMiM3UvsXid2dz3l9lDGTB4d2PpERKTNFIwt8d3rGH/oT8WRQwCYh4Sx91/LANidMYy0GI2PKiLS3SkYW2DbyqUsOjSYPwS9wPX9HmTUmDPxrl8LgGnC5ABXJyIi7UE33/irsoA9B5zkVMVybewkckeHM8QVT3ZhLl6zmTE/vijQFYqISDtQMPrJte1jDlXHYDXZmTztKuLSe+GtqyPtheepz84hdkh6oEsUEZF2oGD004F1nxIf1JdJiZdj/rYe0sHscBB+7rlw7rmBLk9ERNqJrjH6o76a7D0HSQsdhMVkpaCuAK/X4C8r9rL5YClerxHoCkVEpJ0oGP1gZH1BjjOK1JABAKwIXs/3S7+k5K/P8NAf3setYBQR6TF0KtUPBUYyYbaBOCzBlFrKGTJqDIcf/ZCf7/mcEeEm7Fb9fiEi0lPoJ7of3JZg+iePA+DryG2clXoWkVs2ABBz/tQAViYiIu1NR4x+SOk/iDpbHtRDRV8Ph7/bS2xlCfVmK+Munx7o8kREpB3piNEPddnl2OutlFmcDBw1kt0ffgZAbq8hRERHBLg6ERFpTwpGP+SFlPC/Ce/zz7gVTOk1BfM36wCwTz47wJWJiEh706lUPxRaSviu9z7SwtMwylz0OroPgBE/uSTAlYmISHtTMPrhR0k/4uPLP6bKVYVlZxb2qEjc4ZEkDe4b6NJERKSdKRj9ZDKZCLOHwahRDFjzFe7CwkCXJCIiHUDXGFvA7fHi9RqYLBZsSUmBLkdERDqAgrEFPt64n4mPr+SFVVmBLkVERDqIgrEFyl56iT+8dx+xX3wS6FJERKSD6Bqjn2pdHuK2bSSutoLoXnGBLkdERDqIjhj9tO6b3fQry8WLiUGzNNqNiEhPpWD0076PG0a7KU3vhy1OR4wiIj2VgtEPbo8Xx8avAQg5+5wAVyMiIh1JweiHTfvyGZ63G4CBl14Y4GpERKQj6eYbP8Rm78DlrqM2PIrQEcMDXY6IiHQgBaMf0gf1oWzePMwhIZjMOsgWEenJFIx+cPTtS+K9vw50GSIi0gl0+CMiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuJDwSgiIuKjSwfjQw89hMlkOuZr8ODBgS5LRER6MGugCziVYcOGsWLFiqbXVmuXL1lERLqxLp8yVquVpKSkQJchIiKniS4fjHv37iUlJYWgoCAmTJjA448/TkZGxknXr6uro66urul1RUUFAC6XC5fL1ey+Gt8/1XqnK/WneepP89Sf5qk/zWtrf1ryOZNhGEar9tIJPv30UyorKxk0aBBHjx7l4Ycf5vDhw2zbto3w8PATfuahhx7i4YcfPm75W2+9RUhISEeXLCIiXVB1dTVXXXUV5eXlRERENLtulw7GHyorK6NXr148/fTT3HDDDSdc50RHjOnp6RQVFZ2yGS6Xi+XLl3PBBRdgs9natfaeQP1pnvrTPPWneepP89ran4qKCuLi4vwKxi5/KtVXVFQUAwcOZN++fSddx+Fw4HA4jltus9n8bmZL1j0dqT/NU3+ap/40T/1pXmv705LPdOnHNX6osrKSrKwskpOTA12KiIj0UF06GP/nf/6HL7/8kv3797Nu3Touv/xyLBYLV155ZaBLExGRHqpLn0rNzc3lyiuvpLi4mPj4eCZPnszXX39NfHx8oEsTEZEeqksH48KFCwNdgoiInGa69KlUERGRzqZgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8aFgFBER8dEtgvG5556jd+/eBAUFMX78eDZs2BDokkREpIfq8sH4zjvvcPfdd7NgwQK+++47Ro0axYUXXkhBQUGgSxMRkR6oywfj008/zY033sh1113H0KFDefHFFwkJCeGVV14JdGkiItIDdelgrK+vZ9OmTUybNq1pmdlsZtq0aaxfvz6AlYmISE9lDXQBzSkqKsLj8ZCYmHjM8sTERHbt2nXCz9TV1VFXV9f0ury8HICSkhJcLlez+3O5XFRXV1NcXIzNZmtj9T2P+tM89ad56k/z1J/mtbU/TqcTAMMwTrlulw7G1nj88cd5+OGHj1vep0+fAFQjIiJdidPpJDIystl1unQwxsXFYbFYyM/PP2Z5fn4+SUlJJ/zMb37zG+6+++6m116vl5KSEmJjYzGZTM3ur6KigvT0dA4dOkRERETbv4EeRv1pnvrTPPWneepP89raH8MwcDqdpKSknHLdLh2MdrudM844g5UrV3LZZZcBDUG3cuVKbrvtthN+xuFw4HA4jlkWFRXVov1GREToH2Yz1J/mqT/NU3+ap/40ry39OdWRYqMuHYwAd999N3PnzmXcuHGceeaZPPPMM1RVVXHdddcFujQREemBunwwXnHFFRQWFvLggw+Sl5fH6NGjWbp06XE35IiIiLSHLh+MALfddttJT522J4fDwYIFC447FSsN1J/mqT/NU3+ap/40rzP7YzL8uXdVRETkNNGlH/AXERHpbApGERERHwpGERERHwpGERERH6dVMLZ0Xsf33nuPwYMHExQUxIgRI1iyZEknVRo4LenRSy+9xNlnn010dDTR0dFMmzatx8+V2dq5QRcuXIjJZGoaqKKnaml/ysrKmD9/PsnJyTgcDgYOHNij/5+1tD/PPPMMgwYNIjg4mPT0dO666y5qa2s7qdrOtXr1ambOnElKSgomk4nFixef8jOrVq1i7NixOBwO+vfvz2uvvdY+xRiniYULFxp2u9145ZVXjO3btxs33nijERUVZeTn559w/bVr1xoWi8V46qmnjB07dhj333+/YbPZjK1bt3Zy5Z2npT266qqrjOeee87YvHmzsXPnTmPevHlGZGSkkZub28mVd46W9qdRTk6OkZqaapx99tnGpZde2jnFBkBL+1NXV2eMGzfOmDFjhrFmzRojJyfHWLVqlZGZmdnJlXeOlvbnzTffNBwOh/Hmm28aOTk5xmeffWYkJycbd911VydX3jmWLFli3HfffcYHH3xgAMaiRYuaXT87O9sICQkx7r77bmPHjh3Gs88+a1gsFmPp0qVtruW0CcYzzzzTmD9/ftNrj8djpKSkGI8//vgJ1589e7ZxySWXHLNs/Pjxxs0339yhdQZSS3v0Q2632wgPDzdef/31jioxoFrTH7fbbUycONF4+eWXjblz5/boYGxpf1544QWjb9++Rn19fWeVGFAt7c/8+fON884775hld999tzFp0qQOrbMr8CcYf/WrXxnDhg07ZtkVV1xhXHjhhW3e/2lxKrU18zquX7/+mPUBLrzwwh47D2R7zH1ZXV2Ny+UiJiamo8oMmNb253e/+x0JCQnccMMNnVFmwLSmPx999BETJkxg/vz5JCYmMnz4cH7/+9/j8Xg6q+xO05r+TJw4kU2bNjWdbs3OzmbJkiXMmDGjU2ru6jryZ3S3GPmmrVozr2NeXt4J18/Ly+uwOgOpNT36oV//+tekpKQc94+1J2hNf9asWcPf//53MjMzO6HCwGpNf7Kzs/n888+5+uqrWbJkCfv27ePWW2/F5XKxYMGCzii707SmP1dddRVFRUVMnjwZwzBwu93ccsst/Pa3v+2Mkru8k/2MrqiooKamhuDg4FZv+7Q4YpSO98QTT7Bw4UIWLVpEUFBQoMsJOKfTyZw5c3jppZeIi4sLdDldktfrJSEhgb/97W+cccYZXHHFFdx33328+OKLgS6tS1i1ahW///3vef755/nuu+/44IMP+OSTT3jkkUcCXVqPd1ocMbZmXsekpKQWrd/dtaZHjf74xz/yxBNPsGLFCkaOHNmRZQZMS/uTlZXF/v37mTlzZtMyr9cLgNVqZffu3fTr169ji+5Erfn3k5ycjM1mw2KxNC0bMmQIeXl51NfXY7fbO7TmztSa/jzwwAPMmTOHX/ziFwCMGDGCqqoqbrrpJu677z7M5tP7uOZkP6MjIiLadLQIp8kRo++8jo0a53WcMGHCCT8zYcKEY9YHWL58+UnX7+5a0yOAp556ikceeYSlS5cybty4zig1IFran8GDB7N161YyMzObvmbNmsW5555LZmYm6enpnVl+h2vNv59Jkyaxb9++pl8YAPbs2UNycnKPCkVoXX+qq6uPC7/GXyIMDXHdsT+j23z7TjexcOFCw+FwGK+99pqxY8cO46abbjKioqKMvLw8wzAMY86cOca9997btP7atWsNq9Vq/PGPfzR27txpLFiw4LR4XKMlPXriiScMu91uvP/++8bRo0ebvpxOZ6C+hQ7V0v78UE+/K7Wl/Tl48KARHh5u3Hbbbcbu3buNjz/+2EhISDAeffTRQH0LHaql/VmwYIERHh5uvP3220Z2draxbNkyo1+/fsbs2bMD9S10KKfTaWzevNnYvHmzARhPP/20sXnzZuPAgQOGYRjGvffea8yZM6dp/cbHNe655x5j586dxnPPPafHNVrj2WefNTIyMgy73W6ceeaZxtdff9303pQpU4y5c+ces/67775rDBw40LDb7cawYcOMTz75pJMr7nwt6VGvXr0M4LivBQsWdH7hnaSl/4Z89fRgNIyW92fdunXG+PHjDYfDYfTt29d47LHHDLfb3clVd56W9MflchkPPfSQ0a9fPyMoKMhIT083br31VqO0tLTzC+8EX3zxxQl/njT2ZO7cucaUKVOO+8zo0aMNu91u9O3b13j11VfbpRZNOyUiIuLjtLjGKCIi4i8Fo4iIiA8Fo4iIiA8Fo4iIiA8Fo4iIiA8Fo4iIiA8Fo4iIiA8Fo4iIiA8Fo0gAzJs3j8suuyxg+58zZw6///3v27SN1157jaioqBZ95uc//zl/+tOf2rRfkY6mkW9E2pnJZGr2/QULFnDXXXdhGEaLg6U9bNmyhfPOO48DBw4QFhbW6u3U1NTgdDpJSEjw+zPbtm3jnHPOIScnh8jIyFbvW6QjKRhF2pnvZNbvvPMODz74ILt3725aFhYW1qZAaqtf/OIXWK3WgM17+KMf/Yh58+Yxf/78gOxf5FR0KlWknSUlJTV9RUZGYjKZjlkWFhZ23KnUqVOncvvtt3PnnXcSHR1NYmIiL730ElVVVVx33XWEh4fTv39/Pv3002P2tW3bNi6++GLCwsJITExkzpw5FBUVnbQ2j8fD+++/f8w8kQC9e/fm0Ucf5dprryUsLIxevXrx0UcfUVhYyKWXXkpYWBgjR45k48aNTZ/54anUhx56iNGjR/N///d/9O7dm8jISH7+85/jdDqP2dfMmTNZuHBhKzor0jkUjCJdxOuvv05cXBwbNmzg9ttv55e//CU/+9nPmDhxIt999x3Tp09nzpw5VFdXA1BWVsZ5553HmDFj2LhxI0uXLiU/P5/Zs2efdB/ff/895eXlJ5w7889//jOTJk1i8+bNXHLJJcyZM4drr72Wa665hu+++45+/fpx7bXXNjsXYFZWFosXL+bjjz/m448/5ssvv+SJJ544Zp0zzzyTDRs2UFdX18pOiXSwdpmjQ0RO6NVXXzUiIyOPW/7DKaimTJliTJ48uem12+02QkNDj5l/7ujRowZgrF+/3jAMw3jkkUeM6dOnH7PdQ4cOGYCxe/fuE9azaNEiw2KxGF6v95jlvXr1Mq655prj9vXAAw80LVu/fr0BGEePHj3h97ZgwQIjJCTEqKioaFp2zz33GOPHjz9mX1u2bDEAY//+/SesUSTQrAFNZRFpMnLkyKY/WywWYmNjGTFiRNOyxMREAAoKCoCGm2i++OKLE16vzMrKYuDAgcctr6mpweFwnPAGId/9N+7rZPtPSko64ffQu3dvwsPDm14nJyc31dsoODgYoOnIV6SrUTCKdBE2m+2Y1yaT6ZhljWHm9XoBqKysZObMmTz55JPHbSs5OfmE+4iLi6O6upr6+nrsdvtJ99+4r+b27+/38MP1S0pKAIiPjz/pdkQCScEo0k2NHTuWf/7zn/Tu3Rur1b//yqNHjwZgx44dTX/ubNu2bSMtLY24uLiA7F/kVHTzjUg3NX/+fEpKSrjyyiv59ttvycrK4rPPPuO6667D4/Gc8DPx8fGMHTuWNWvWdHK1//HVV18xffr0gO1f5FQUjCLdVEpKCmvXrsXj8TB9+nRGjBjBnXfeSVRUFGbzyf9r/+IXv+DNN9/sxEr/o7a2lsWLF3PjjTcGZP8i/tAD/iKnmZqaGgYNGsQ777zDhAkTOnXfL7zwAosWLWLZsmWdul+RltARo8hpJjg4mDfeeKPZgQA6is1m49lnn+30/Yq0hI4YRUREfOiIUURExIeCUURExIeCUURExIeCUURExIeCUURExIeCUURExIeCUURExIeCUURExIeCUURExMf/B7cmZFmKRvrqAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["min diff: 0.8710225803485301 at idx: 0\n", "Average PCE absolute difference: 2.721479192110164\n", "Average PCE diff across device: 0.5532811132482216\n", "\n", "min diff: 0.42297655807788814 at idx: 4\n", "Average PCE absolute difference: 1.6567733297514664\n", "Average PCE diff across device: 0.2946782923304454\n", "\n", "min diff: 0.15170070398881563 at idx: 1\n", "Average PCE absolute difference: 2.4328876450989165\n", "Average PCE diff across device: 1.7988672349595234\n", "\n", "min diff: 0.25024051518720114 at idx: 7\n", "Average PCE absolute difference: 0.9840353572785094\n", "Average PCE diff across device: 0.45681852456299055\n", "\n", "total_diff 1.9144606839353282\n", "total device diff 0.7759112912752952\n"]}], "source": ["# %matplotlib widget\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import numpy_indexed as npi\n", "from labellines import labelLines\n", "from matplotlib.font_manager import FontProperties\n", "import os\n", "import sys\n", "import re\n", "np.set_printoptions(threshold=sys.maxsize)\n", "from matplotlib.cbook import boxplot_stats\n", "\n", "def get_outlier_indices(data):\n", "\tdata = np.asarray(data)\n", "\tq1 = np.percentile(data, 25)\n", "\tq3 = np.percentile(data, 75)\n", "\tiqr = q3 - q1\n", "\n", "\tlower_bound = q1 - 1.5 * iqr\n", "\tupper_bound = q3 + 1.5 * iqr\n", "\n", "\toutlier_mask = (data < lower_bound) | (data > upper_bound)\n", "\treturn np.where(outlier_mask)[0]\n", "\n", "def detect_settled_index(t, P, window, thresh):\n", "\tt = np.asarray(t, dtype=float)\n", "\tP = np.asarray(P, dtype=float)\n", "\n", "\t# --- rest of your function unchanged --------------------\n", "\tfor start in np.arange(t[0], t[-1] - window, window):\n", "\t\tend   = start + window\n", "\t\tmask  = (t >= start) & (t <= end)\n", "\t\tif mask.sum() < 2:\n", "\t\t\tcontinue\n", "\t\tsegment   = P[mask]\t\t  # now works: P is an ndarray\n", "\t\tmean_seg  = segment.mean()\n", "\t\tif mean_seg == 0:\n", "\t\t\tcontinue\n", "\t\tif np.abs(segment - mean_seg).max() < thresh * mean_seg:\n", "\t\t\treturn np.where(t >= start)[0][0]   # first index in settled window\n", "\treturn None\n", "\n", "def plot_current(mA_density, cas_mA, time_label, ss_file_path, time, cas_directory, cas_time):\n", "\tbottom = min(np.min(mA_density), np.min(cas_mA))*0.95\n", "\ttop = max(np.max(mA_density), np.max(cas_mA))*1.05\n", "\n", "\tplt.figure(figsize=plot_size)\n", "\tplt.ylim(bottom = bottom, top = top)\n", "\tplt.xlabel(time_label)\n", "\tplt.grid()\n", "\tplt.ylabel('Current Density (mA / cm^2)')\n", "\tplt.subplots_adjust(left=0.086,\n", "\t\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\t\tright=0.844,\n", "\t\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\t\thspace=0.2)\n", "\tcolors = plt.rcParams['axes.prop_cycle'].by_key()['color']\n", "\n", "\tfor i in plot_range:\n", "\t\tlineName = \"mA\" + str(i + 1)\n", "\t\tif ss_file_path:\n", "\t\t\tplt.plot(time,\n", "\t\t\t\t\t\tmA_density[:,i],\n", "\n", "\t\t\t\t\t\tcolor=colors[i % len(colors)],\n", "\t\t\t\t\t\tlabel = \"SMS\"+ str(i+1))\n", "\t\tif cas_directory:\n", "\t\t\tplt.plot(cas_time[i],\n", "\t\t\t\t\t\tcas_mA[i],\n", "\t\t\t\t\t\tlinestyle='--',\n", "\t\t\t\t\t\tcolor=colors[i % len(colors)],\n", "\t\t\t\t\t\tlabel = \"CAS\")\n", "\n", "\t# plt.legend(bbox_to_anchor=legend_anchor)\n", "\tplt.legend()\n", "\tplt.show()\n", "\n", "def plot_voltage(v, cas_V, time_label, ss_file_path, time, cas_directory, cas_time):\n", "\tplt.figure(figsize=plot_size)\n", "\tbottom = min(np.min(v), np.min(cas_V))*0.95\n", "\ttop = max(np.max(v), np.max(cas_V))*1.05\n", "\tplt.ylim(bottom = bottom, top = top)\n", "\tplt.xlabel(time_label)\n", "\tplt.grid()\n", "\tplt.ylabel('Voltage (V)')\n", "\tplt.subplots_adjust(left=0.086,\n", "\t\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\t\tright=0.844,\n", "\t\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\t\thspace=0.2)\n", "\tcolors = plt.rcParams['axes.prop_cycle'].by_key()['color']\n", "\n", "\tfor i in plot_range:\n", "\t\tlineName = \"V\" + str(i + 1)\n", "\t\tif ss_file_path:\n", "\t\t\tplt.plot(time,\n", "\t\t\t\t\t v[:,i],\n", "\n", "\t\t\t\t\t color=colors[i % len(colors)],\n", "\t\t\t\t\t label = \"SMS\"+ str(i+1))\n", "\t\tif cas_directory:\n", "\t\t\tplt.plot(cas_time[i],\n", "\t\t\t\t\t cas_V[i],\n", "\t\t\t\t\t linestyle='--',\n", "\t\t\t\t\t color=colors[i % len(colors)],\n", "\t\t\t\t\t label = \"CAS\")\n", "\t# plt.legend(bbox_to_anchor=legend_anchor)\n", "\tplt.legend()\n", "\tplt.show()\n", "\n", "def plot_difference(cas_directory = \"\", ss_file_path = \"\", plot_range = [], time_limit = -1, plot_size = (5,5), remove_outliers = False, roll = 0, plot = True):\n", "\tall_files = []\n", "\tcell_area = 0\n", "\tif ss_file_path != \"\":\n", "\t\tarr = np.loadtxt(ss_file_path, delimiter=\",\", dtype=str)\n", "\t\theader_row = np.where(arr == \"Time\")[0][0]\n", "\n", "\t\tmeta_data = {}\n", "\t\tfor data in arr[:header_row, :2]:\n", "\t\t\tmeta_data[data[0]] = data[1]\n", "\t\tcell_area= float(meta_data[\"Cell Area (mm^2)\"])\n", "\n", "\tif cas_directory != \"\":\n", "\t\tfor root, dirs, files in os.walk(cas_directory):\n", "\t\t\tfor file in files:\n", "\t\t\t\tif file.endswith('.csv'):\n", "\t\t\t\t\tfile_path = os.path.join(root, file)\n", "\t\t\t\t\tall_files.append(file_path)\n", "\n", "\tcas_pce = []\n", "\tcas_V = []\n", "\tcas_mA = []\n", "\tcas_time = []\n", "\n", "\t# Process each file\n", "\tfor file_path in all_files:\n", "\t\tif \"_5_0\" in file_path:\n", "\t\t\ttry:\n", "\t\t\t\tdata = np.genfromtxt(\n", "\t\t\t\t\tfile_path,\n", "\t\t\t\t\tdelimiter=',',\n", "\t\t\t\t\tcomments='#',\n", "\t\t\t\t\tskip_header=0,\n", "\t\t\t\t\tencoding='cp1252'  # Specify the correct encoding\n", "\t\t\t\t)\n", "\t\t\t\tpce = ((data[:, 1] * (data[:, 2]/1000)) / (0.1*cell_area))*100\n", "\t\t\t\t# data_with_pce= np.column_stack((data, pce))\n", "\t\t\t\tcas_V.append(data[:, 1])\n", "\t\t\t\tcas_mA.append(data[:, 2]/cell_area)\n", "\t\t\t\tcas_time.append(data[:,0])\n", "\t\t\t\tcas_pce.append(pce)\n", "\t\t\texcept Exception as e:\n", "\t\t\t\tprint(f\"Error processing {file_path}: {e}\")\n", "\n", "\tcas_pce = np.array(cas_pce[::-1])\n", "\tcas_time = np.array(cas_time[::-1])\n", "\tcas_mA = np.array(cas_mA[::-1])\n", "\tcas_V = np.array(cas_V[::-1])\n", "\tcas_time = cas_time/60\n", "\tif cas_directory:\n", "\t\tmax_time = np.max(cas_time[0])\n", "\t\tif max_time > time_limit and time_limit > 0:\n", "\t\t\tend_idx = np.searchsorted(cas_time[0,:], time_limit)\n", "\n", "\t\t\tcas_time = cas_time[:, :end_idx]\n", "\t\t\tcas_pce = cas_pce[:, :end_idx]\n", "\t\t\tcas_mA = cas_mA[:, :end_idx]\n", "\t\t\tcas_V = cas_V[:, :end_idx]\n", "\telse:\n", "\t\tcas_mA = [0]\n", "\t\tcas_V = [0]\n", "\t\tcas_pce = [0]\n", "\n", "\n", "\tdata = []\n", "\tmA_density = []\n", "\tv = []\n", "\tcell_area = 0\n", "\t# ss_file_path = \"\"\n", "\t# ss_file_path = r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Mar-4-2025 4 device test\\Mar-04-2025 16_42_19__4 Device First Test\\Mar-04-2025_16-44-53__4 Device First Test__ID2__mppt.csv\"\n", "\tif ss_file_path != \"\":\n", "\t\tarr = np.loadtxt(ss_file_path, delimiter=\",\", dtype=str)\n", "\t\theader_row = np.where(arr == \"Time\")[0][0]\n", "\n", "\t\tmeta_data = {}\n", "\t\tfor data in arr[:header_row, :2]:\n", "\t\t\tmeta_data[data[0]] = data[1]\n", "\n", "\t\theaders = arr[header_row, :]\n", "\t\tarr = arr[header_row + 1 :, :]\n", "\n", "\t\theader_dict = {value: index for index, value in enumerate(headers)}\n", "\t\ttime = np.array(arr[:, header_dict[\"Time\"]]).astype(\"float\")\n", "\t\ttime /= 60\n", "\n", "\t\tpixel_V = arr[:, 1::2][:, 0:8].astype(float)\n", "\t\tpixel_mA = arr[:, 2::2][:, 0:8].astype(float)\n", "\t\tpixel_V = np.roll(pixel_V, roll, axis = 1)\n", "\t\tpixel_mA = np.roll(pixel_mA, roll, axis = 1)\n", "\n", "\t\tcell_area = float(meta_data[\"Cell Area (mm^2)\"])\n", "\t\tmA_density = pixel_mA/cell_area\n", "\n", "\t\tv = pixel_V\n", "\n", "\t\tdata = ((pixel_V*pixel_mA/1000) / (0.1*cell_area))*100\n", "\n", "\t\t# calculation in minutes\n", "\t\tmax_time = time[-1]\n", "\t\tif max_time > time_limit and time_limit > 0:\n", "\t\t\tend_idx = np.searchsorted(time, time_limit)\n", "\t\t\ttime = time[:end_idx]\n", "\t\t\tdata = data[:end_idx,:]\n", "\t\t\tmA_density = mA_density[:end_idx,:]\n", "\t\t\tv = pixel_V[:end_idx,:]\n", "\ttime_label = \"Time (min)\"\n", "\tif max(time) > 60:\n", "\t\ttime /= 60\n", "\t\tcas_time /= 60\n", "\t\ttime_label = \"Time (hr)\"\n", "\n", "\taverage_cas = []\n", "\taverage_sms = []\n", "\tif plot:\n", "\t\tplt.figure(figsize=plot_size)\n", "\t\tbottom = min(np.min(cas_pce), np.min(data))*0.95\n", "\t\ttop = max(np.max(cas_pce), np.max(data))*1.05\n", "\n", "\t\tplt.ylim(bottom = 0, top = top)\n", "\t\tplt.xlabel(time_label)\n", "\t\tplt.grid()\n", "\t\tcolors = plt.rcParams['axes.prop_cycle'].by_key()['color']\n", "\n", "\t\tplt.ylabel('PCE (%)')\n", "\t\t# plt.subplots_adjust(left=0.086,\n", "\t\t# \t\t\t\t\tbottom=0.06,\n", "\t\t# \t\t\t\t\tright=0.844,\n", "\t\t# \t\t\t\t\ttop=0.927,\n", "\t\t# \t\t\t\t\twspace=0.2,\n", "\t\t# \t\t\t\t\thspace=0.2)\n", "\tfor i in plot_range:\n", "\t\tif ss_file_path and plot:\n", "\t\t\tplt.plot(time,\n", "\t\t\t\t\tdata[:,i],\n", "\t\t\t\t\tcolor=colors[i % len(colors)],\n", "\t\t\t\t\tlabel = \"SMS\"+ str(i+1))\n", "\t\tif cas_directory and plot:\n", "\t\t\tplt.plot(cas_time[i],\n", "\t\t\t\t\tcas_pce[i],\n", "\t\t\t\t\tlinestyle='--',\n", "\t\t\t\t\tcolor=colors[i % len(colors)],\n", "\t\t\t\t\tlabel = \"CAS\"+ str(i+1))\n", "\t\tif ss_file_path and cas_directory:\n", "\t\t\tsecond_half = data[:,i][len(data[:,i]) // 2:]\n", "\t\t\taverage_second_half = np.mean(second_half)\n", "\n", "\t\t\tsecond_half_cas = cas_pce[i][len(cas_pce[i]) // 2:]\n", "\t\t\taverage_second_half_cas = np.mean(second_half_cas)\n", "\t\t\tif average_second_half > 2.5 and average_second_half_cas > 2.5:\n", "\t\t\t\taverage_sms.append(average_second_half)\n", "\t\t\t\taverage_cas.append(average_second_half_cas)\n", "\n", "\t\tif plot:\n", "\t\t\tlines = plt.gca().get_lines()\n", "\t\t\tx_min, x_max = plt.xlim()\n", "\t\t\tnum_lines = len(lines)\n", "\t\t# legend_anchor = (1, 0.2)\n", "\t\t# plt.legend(bbox_to_anchor=legend_anchor)\n", "\tif plot:\n", "\t\t# plt.legend()\n", "\t\tplt.savefig(\n", "\t\t\t\"MPPT_Plot.png\",         # output filename (extension defines format)\n", "\t\t)\n", "\t\t# plt.show()\n", "\n", "\t# plot_current(mA_density, cas_mA, time_label, ss_file_path, time, cas_directory, cas_time)\n", "\t# plot_voltage(v, cas_V, time_label, ss_file_path, time, cas_directory, cas_time)\n", "\n", "\n", "\n", "\taverage_sms = np.array(average_sms)\n", "\taverage_cas = np.array(average_cas)\n", "\tabs_diff = abs(average_sms - average_cas)\n", "\n", "\ttry:\n", "\t\tsms_outliers = get_outlier_indices(average_sms)\n", "\t\tcas_outliers = get_outlier_indices(average_cas)\n", "\t\toutliers = np.unique(np.concatenate((sms_outliers, cas_outliers)))\n", "\texcept:\n", "\t\toutliers = []\n", "\tfiltered_sms = np.delete(average_sms, outliers)\n", "\tfiltered_cas = np.delete(average_cas, outliers)\n", "\tfiltered_diff = abs(filtered_sms-filtered_cas)\n", "\n", "\t# Plot the boxplot\n", "\t# if filtered:\n", "\t\t# box_plot = [filtered_sms, filtered_cas]\n", "\t# elif not filtered:\n", "\n", "\tif plot:\n", "\t\tbox_plot = [average_sms, average_cas]\n", "\t\tlabels = [\"SMS\", \"CAS\"]\n", "\n", "\t\t# Optional: compute stats using <PERSON><PERSON>lotlib's utility\n", "\t\tstats = boxplot_stats(box_plot)\n", "\t\tfig, ax = plt.subplots(figsize=plot_size)\n", "\t\tbp = ax.boxplot(box_plot, flierprops=dict(marker='o', markersize=0))\n", "\t\tax.set_ylim(0, 25)\n", "\n", "\t\tfor i, stat in enumerate(stats, start=1):\n", "\t\t\toutliers = stat['fliers']  # already identified!\n", "\n", "\t\t\tif len(outliers) > 0:\n", "\t\t\t\tjitter = 0.2 * (np.random.rand(len(outliers)) - 0.5)  # random horizontal spread\n", "\t\t\t\tx_positions = np.full(len(outliers), i) + jitter\n", "\t\t\t\tax.scatter(x_positions, outliers, color='white', edgecolor='black', zorder=3)\n", "\n", "\t\t# Label the x-axis\n", "\t\tax.set_xticks([1, 2])\n", "\t\tax.set_title(\"SMS vs CAS PCE\")\n", "\t\tax.set_ylabel(\"PCE (%)\")\n", "\t\tax.set_xticklabels(labels)\n", "\t\tplt.savefig(\n", "\t\t\t\"Comparison_PCE_BoxPlot.png\",         # output filename (extension defines format)\n", "\t\t)\n", "\t\tplt.show()\n", "\n", "\n", "\tif remove_outliers:\n", "\t\treturn filtered_diff, filtered_sms, filtered_cas\n", "\telse:\n", "\t\treturn abs_diff, average_sms, average_cas\n", "\n", "r\"\"\"\n", "Best\n", "\n", "cas search = _5_0\n", "\n", "for i in range(1,5):\n", "\tfiles.append([\n", "\t\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\April-03-2025 cas\\devices\\{i}\",\n", "\t\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-03-2025 19_41_27\\Apr-03-2025_19-57-33__ID{i}__mppt.csv\"\n", "\t])\n", "\n", "\"\"\"\n", "\n", "# # TRIAL WITH CAS HOLDER\n", "# sms_files = [\n", "#     rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-03-2025 19_41_27\\Apr-03-2025_19-57-33__ID1__mppt.csv\",\n", "#     rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-03-2025 19_41_27\\Apr-03-2025_19-57-33__ID2__mppt.csv\",\n", "#     rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-03-2025 19_41_27\\Apr-03-2025_19-57-33__ID3__mppt.csv\",\n", "#     rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-03-2025 19_41_27\\Apr-03-2025_19-57-33__ID4__mppt.csv\",\n", "# ]\n", "\n", "# cas_files = [\n", "# \trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-03-2025 19_41_27\\devices\\1\",\n", "# \trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-03-2025 19_41_27\\devices\\2\",\n", "# \trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-03-2025 19_41_27\\devices\\3\",\n", "# \trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-03-2025 19_41_27\\devices\\4\",\n", "# ]\n", "\n", "# TRIAL WITH SAMPLE HOLDER\n", "sms_files = [\n", "\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\Apr-29-2025_17-27-31__ID1__mppt.csv\",\n", "\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\Apr-29-2025_17-33-16__ID2__mppt.csv\",\n", "\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\Apr-29-2025_17-33-16__ID3__mppt.csv\",\n", "\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\Apr-29-2025_17-27-31__ID4__mppt.csv\",\n", "]\n", "cas_files = [\n", "\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\devices\\1\",\n", "\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\devices\\2\",\n", "\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\devices\\3\",\n", "\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\devices\\4\",\n", "]\n", "\n", "plot_range = [x for x in range(8)]\n", "printouts = []\n", "all_diff = []\n", "average_sms = []\n", "average_cas = []\n", "average_diff_device = []\n", "plot_size = (5,5)\n", "for cas, ss_file in zip(cas_files, sms_files):\n", "\tabs_diff, temp_sms, temp_cas = plot_difference(cas, ss_file, plot_range, plot_size = plot_size, remove_outliers=False, roll = 0, time_limit = 1)\n", "\tsms = np.mean(temp_sms)\n", "\tcas = np.mean(temp_cas)\n", "\tdevice_diff = abs(sms - cas)\n", "\taverage_diff_device.append(device_diff)\n", "\n", "\tall_diff.extend(abs_diff)\n", "\taverage_sms.extend(temp_sms)\n", "\taverage_cas.extend(temp_cas)\n", "\t# print(\"sms:\" ,temp_sms)\n", "\t# print(\"lit:\" ,temp_cas)\n", "\tif cas and ss_file:\n", "\t\ttry:\n", "\t\t\tprintouts.append([np.min(abs_diff), np.argmin(abs_diff), np.mean(abs_diff), device_diff])\n", "\n", "\t\texcept:\n", "\t\t\tpass\n", "\n", "average_sms = np.asarray(average_sms)\n", "average_cas = np.asarray(average_cas)\n", "average_diff = average_sms - average_cas\n", "\n", "\n", "# # Print stats for each label\n", "# for label, stat in zip(labels, stats):\n", "#     print(f\"--- {label} ---\")\n", "#     print(f\"Min: {stat['whislo']}\")\n", "#     print(f\"Q1 : {stat['q1']}\")\n", "#     print(f\"Med: {stat['med']}\")\n", "#     print(f\"Q3 : {stat['q3']}\")\n", "#     print(f\"Max: {stat['whishi']}\")\n", "#     print(f\"Outliers: {stat['fliers']}\")\n", "#     print()\n", "\n", "for printout in printouts:\n", "\tprint(f\"min diff: {printout[0]} at idx: {printout[1]}\")  # or arr.min()\n", "\tprint(f\"Average PCE absolute difference: {printout[2]}\")\n", "\tprint(f\"Average PCE diff across device: {printout[3]}\")\n", "\tprint()\n", "print(f\"total_diff {np.mean(all_diff)}\")\n", "print(f\"total device diff {np.mean(average_diff_device)}\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1560 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(1500, 1560)\n", "(1500, 1560)\n"]}], "source": ["\n", "box_plot = [average_sms, average_cas]\n", "labels = [\"SMS\", \"CAS\"]\n", "\n", "# Optional: compute stats using <PERSON><PERSON>lotlib's utility\n", "stats = boxplot_stats(box_plot)\n", "\n", "FIGSIZE = (5, 5.2)     # e.g. 8.6 cm × 6.6 cm  (single-column width)\n", "DPI     = 300\n", "fig, ax = plt.subplots(figsize=FIGSIZE, dpi=DPI)\n", "bp = ax.boxplot(box_plot, flierprops=dict(marker='o', markersize=0))\n", "\n", "for i, stat in enumerate(stats, start=1):\n", "\toutliers = stat['fliers']  # already identified!\n", "\n", "\tif len(outliers) > 0:\n", "\t\tjitter = 0.2 * (np.random.rand(len(outliers)) - 0.5)  # random horizontal spread\n", "\t\tx_positions = np.full(len(outliers), i) + jitter\n", "\t\tax.scatter(x_positions, outliers, color='white', edgecolor='black', zorder=3)\n", "\n", "# Label the x-axis\n", "ax.set_xticks([1, 2])\n", "ax.set_title(\"SMS vs CAS PCE\")\n", "ax.set_ylabel(\"PCE (%)\")\n", "ax.set_xticklabels(labels, rotation=45, ha='right')\n", "\n", "fig.savefig(\"Comparison_PCE_BoxPlot.png\", dpi=DPI, bbox_inches=None,   # make sure nothing trims your canvas\n", "            pad_inches=0)\n", "plt.show()\n", "print(fig.canvas.get_width_height())\n", "\n", "from PIL import Image\n", "im = Image.open(\"Comparison_PCE_BoxPlot.png\")\n", "print(im.size)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PCE bias = 0.4628\n", "PCE mae = 1.9145\n", "PCE rmse = 2.6241\n", "PCE ci95 = (-0.5572, 1.4827)\n", "PCE loa  = (-4.6928, 5.6184)\n", "\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["import numpy as np\n", "from scipy import stats\n", "\n", "plot_range = [x for x in range(8)]\n", "printouts = []\n", "all_diff = []\n", "average_sms = []\n", "average_cas = []\n", "average_diff_device = []\n", "plot_size = (10,4)\n", "for cas, ss_file in zip(cas_files, sms_files):\n", "\tabs_diff, temp_sms, temp_cas = plot_difference(cas, ss_file, plot_range, plot_size = plot_size, remove_outliers=False, roll = 0, plot = False, time_limit=1)\n", "\tsms = np.mean(temp_sms)\n", "\tcas = np.mean(temp_cas)\n", "\tdevice_diff = abs(sms - cas)\n", "\taverage_diff_device.append(device_diff)\n", "\n", "\tall_diff.extend(abs_diff)\n", "\taverage_sms.extend(temp_sms)\n", "\taverage_cas.extend(temp_cas)\n", "\t# print(\"sms:\" ,temp_sms)\n", "\t# print(\"lit:\" ,temp_cas)\n", "\tif cas and ss_file:\n", "\t\ttry:\n", "\t\t\tprintouts.append([np.min(abs_diff), np.argmin(abs_diff), np.mean(abs_diff), device_diff])\n", "\n", "\t\texcept:\n", "\t\t\tpass\n", "\n", "average_sms = np.asarray(average_sms)\n", "average_cas = np.asarray(average_cas)\n", "average_diff = average_sms - average_cas\n", "\n", "\n", "bias = average_diff.mean()\n", "mae = np.abs(average_diff).mean()\n", "rmse = np.sqrt((average_diff**2).mean())\n", "sd   = average_diff.std(ddof=1)\n", "ci95 = stats.t.interval(0.95, len(average_diff)-1, loc=bias, scale=sd/np.sqrt(len(average_diff)))\n", "loa  = (bias - 1.96*sd, bias + 1.96*sd)\n", "ci95 = (round(ci95[0], 4), round(ci95[1], 4))\n", "loa  = (round(loa[0],  4), round(loa[1],  4))\n", "print(f\"PCE bias = {bias:.4f}\")\n", "print(f\"PCE mae = {mae:.4f}\")\n", "print(f\"PCE rmse = {rmse:.4f}\")\n", "print(f\"PCE ci95 = {ci95}\")\n", "print(f\"PCE loa  = {loa}\")\n", "print()\n", "\n", "\n", "# PCE bias = -0.0278\n", "# PCE sd   = 1.7007\n", "# PCE rmse = 1.6723\n", "# PCE ci95 = (-0.662819110734372, 0.6072758972374629)\n", "# PCE loa  = (-3.3611209689704675, 3.3055777554735584)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'max_pce' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[26], line 4\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m8\u001b[39m):\n\u001b[0;32m      3\u001b[0m \tplt\u001b[38;5;241m.\u001b[39mfigure(figsize\u001b[38;5;241m=\u001b[39mplot_size)\n\u001b[1;32m----> 4\u001b[0m \tplt\u001b[38;5;241m.\u001b[39mylim(bottom \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m0\u001b[39m, top \u001b[38;5;241m=\u001b[39m \u001b[43mmax_pce\u001b[49m)\n\u001b[0;32m      5\u001b[0m \tplt\u001b[38;5;241m.\u001b[39mxlabel(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mTime [min]\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m      6\u001b[0m \tplt\u001b[38;5;241m.\u001b[39mgrid()\n", "\u001b[1;31mNameError\u001b[0m: name 'max_pce' is not defined"]}, {"data": {"text/plain": ["<Figure size 1000x400 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["errors = []\n", "for i in range(8):\n", "\tplt.figure(figsize=plot_size)\n", "\tplt.ylim(bottom = -0, top = max_pce)\n", "\tplt.xlabel('Time [min]')\n", "\tplt.grid()\n", "\n", "\tplt.ylabel('PCE [%]')\n", "\tplt.subplots_adjust(left=0.086,\n", "\t\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\t\tright=0.844,\n", "\t\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\t\thspace=0.2)\n", "\tpercent_error = float(\"inf\")\n", "\n", "\tlineName = \"PCE\" + str(i + 1)\n", "\tif ss_file_path:\n", "\t\tplt.plot(time,data[:,i], label = lineName)\n", "\tif cas_time:\n", "\t\tplt.plot(cas_time[i], cas_pce[i], label = \"cas\" + str(i+1))\n", "\tif ss_file_path and cas_time:\n", "\t\tsecond_half_cas = cas_pce[i][len(cas_pce[i]) // 2:]\n", "\t\taverage_second_half_cas = np.mean(second_half_cas)\n", "\n", "\t\tsecond_half = data[:,i][len(data[:,i]) // 2:]\n", "\t\taverage_second_half = np.mean(second_half)\n", "\n", "\t\tpercent_error = abs(average_second_half - average_second_half_cas) / abs(average_second_half_cas) * 100\n", "\n", "\tlabelLines(plt.gca().get_lines(), zorder=2.5)\n", "\tplt.title(\"Percent Error: \"+ str(percent_error))\n", "\tplt.legend(bbox_to_anchor=(1.15, 1))\n", "for idx, i in enumerate(errors):\n", "\tprint(idx+1, i)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "cas_directory = \"\"\n", "cas_directory = r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Mar-4-2025 4 device test\\2025-03-04-- cas New PCB 4 Device comparison\\devices\\device_4\"\n", "\n", "all_files = []\n", "\n", "if cas_directory != \"\":\n", "\tfor root, dirs, files in os.walk(cas_directory):\n", "\t\tfor file in files:\n", "\t\t\tif file.endswith('.csv'):\n", "\t\t\t\tfile_path = os.path.join(root, file)\n", "\t\t\t\tall_files.append(file_path)\n", "\n", "\n", "cas_pce = []\n", "cas_time = []\n", "\n", "# Process each file\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#plot cas uneven length\n", "\n", "cas_directory = \"\"\n", "cas_directory = r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\2024-11-06 --cas vs stability setup long test\\nov6_2024 test 1\\devices\"\n", "\n", "\n", "\n", "def process_files(cas_directory, all_files, pattern):\n", "\tall_files = []\n", "\n", "\tif cas_directory != \"\":\n", "\t\tfor root, dirs, files in os.walk(cas_directory):\n", "\t\t\tfor file in files:\n", "\t\t\t\tif file.endswith('.csv'):\n", "\t\t\t\t\tfile_path = os.path.join(root, file)\n", "\t\t\t\t\tall_files.append(file_path)\n", "\n", "\ttimes = []\n", "\tpces = []\n", "\tfor file_path in all_files:\n", "\t\tif pattern in file_path:\n", "\t\t\ttry:\n", "\t\t\t\tdata = np.genfromtxt(\n", "\t\t\t\t\tfile_path,\n", "\t\t\t\t\tdelimiter=',',\n", "\t\t\t\t\tcomments='#',\n", "\t\t\t\t\tskip_header=0,\n", "\t\t\t\t\tencoding='cp1252'  # Specify the correct encoding\n", "\t\t\t\t)\n", "\t\t\t\tpce = (data[:, 1] * data[:, 2] / 1000) / (0.1 * 0.128) * 100\n", "\t\t\t\ttimes.append(data[:, 0])\n", "\t\t\t\tpces.append(pce)\n", "\t\t\texcept Exception as e:\n", "\t\t\t\tprint(f\"Error processing {file_path}: {e}\")\n", "\n", "\tpces = pces[::-1]\n", "\treturn times, pces\n", "\n", "def calculate_second_half_averages(pces):\n", "\taverages = []\n", "\tfor pce in pces[:8]:  # Limit to first 8 files, as per the original code\n", "\t\tsecond_half = pce[len(pce) // 2:]\n", "\t\taverages.append(np.mean(second_half))\n", "\treturn averages\n", "\n", "# Process files for each pattern\n", "cas_time1, cas_pce1 = process_files(cas_directory, all_files, \"_3_0\")\n", "cas_time2, cas_pce2 = process_files(cas_directory, all_files, \"_7_0\")\n", "\n", "# Calculate averages for each pattern\n", "cas_beginning_avg = calculate_second_half_averages(cas_pce1)\n", "cas_ending_avg = calculate_second_half_averages(cas_pce2)\n", "\n", "print(cas_beginning_avg)\n", "print(cas_ending_avg)\n", "\n", "\n", "data = []\n", "\n", "ss_file_path = \"\"\n", "ss_file_path = r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\2024-11-06 --cas vs stability setup long test\\Nov-06-2024 13_50_53\\Nov-06-2024 13_50_53ID2PnO.csv\"\n", "if ss_file_path != \"\":\n", "\tarr = np.loadtxt(ss_file_path,\n", "\t\t\t\t\t\tdelimiter=\",\",\n", "\t\t\t\t\t\tdtype=str)\n", "\n", "\n", "\theaders = arr[6,:]\n", "\theader_dict = {value: index for index, value in enumerate(headers)}\n", "\tpce_indicies = [header_dict[value] for value in header_dict if \"PCE\" in value]\n", "\tarr = arr[7:, :]\n", "\n", "\ttime = np.array(arr[:,header_dict[\"Time\"]]).astype('float')\n", "\ttime/=60\n", "\tpce_list = np.array(arr)\n", "\tpce_list = pce_list[:, pce_indicies]\n", "\t# pce_list = pce_list[:,0:-1]\n", "\tfor i in range(len(pce_list)):\n", "\t\tpce_list[i] = [float(j) if j != \" ovf\" else 0.0 for j in pce_list[i]]\n", "\t\tpce_list[i] = [float(j) if j != \"nan\" else 0.0 for j in pce_list[i]]\n", "\n", "\tpce_list = pce_list.astype(float)\n", "\n", "\tdata = pce_list #np.array(data).T\n", "\t# data *= 2.048 # comment line if not using mask\n", "\n", "# min_time = min(time)*0.99\n", "# max_time = max(time)*1.01\n", "plot_size = (12,8)\n", "min_pce = 0\n", "max_pce = 15\n", "\n", "plt.figure(figsize=plot_size)\n", "# plt.xlim(min_time,max_time)\n", "plt.ylim(bottom = min_pce, top = max_pce)\n", "plt.xlabel('Time [min]')\n", "plt.grid()\n", "\n", "plt.ylabel('PCE [%]')\n", "plt.subplots_adjust(left=0.086,\n", "\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\tright=0.844,\n", "\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\thspace=0.2)\n", "\n", "errors = []\n", "\n", "for i in range(8):\n", "\tlineName = \"PCE\" + str(i + 1)\n", "\t# print(np.array(pce_list[i]))\n", "\tif data != []:\n", "\t\tplt.plot(time,data[:,i], label = lineName)\n", "\tif i < len(cas_beginning_avg) and i < len(cas_ending_avg):\n", "\t\t# Calculate straight line\n", "\t\ty_values = [\n", "\t\t\tcas_beginning_avg[i],\n", "\t\t\tcas_ending_avg[i]\n", "\t\t]\n", "\t\tprint(y_values)\n", "\t\tx_values = [time[0], time[-1]]\n", "\t\tplt.plot(x_values, y_values, linestyle=\"--\", label=f\"cas {i + 1}\")\n", "\n", "print(np.mean(errors))\n", "\n", "lines = plt.gca().get_lines()\n", "x_min, x_max = plt.xlim()\n", "num_lines = len(lines)\n", "xvals = np.linspace(x_min + 0.1 * (x_max - x_min), x_max - 0.1 * (x_max - x_min), num_lines)\n", "bold_font = FontProperties(weight='medium')\n", "labelLines(\n", "\tlines,\n", "\txvals=xvals,\n", "\tzorder=2.5,\n", "\talign=False,\n", "\tfontsize=11,\n", "\tfontproperties=bold_font\n", ")\n", "plt.legend(bbox_to_anchor=(1.15, 1))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["errors = []\n", "for i in range(8):\n", "\tplt.figure(figsize=plot_size)\n", "\tplt.ylim(bottom = -0, top = max_pce)\n", "\tplt.xlabel('Time [min]')\n", "\tplt.grid()\n", "\n", "\tplt.ylabel('PCE [%]')\n", "\tplt.subplots_adjust(left=0.086,\n", "\t\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\t\tright=0.844,\n", "\t\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\t\thspace=0.2)\n", "\n", "\n", "\tlineName = \"PCE\" + str(i + 1)\n", "\t# print(np.array(pce_list[i]))\n", "\tplt.plot(time,data[:,i], label = lineName)\n", "\tif i < len(cas_beginning_avg) and i < len(cas_ending_avg):\n", "\t\t# Calculate straight line\n", "\t\ty_values = [\n", "\t\t\tcas_beginning_avg[i],\n", "\t\t\tcas_ending_avg[i]\n", "\t\t]\n", "\t\tx_values = [time[0], time[-1]]\n", "\t\tplt.plot(x_values, y_values, linestyle=\"--\", label=f\"cas {i + 1}\")\n", "\n", "\tlast_portion = data[:,i][99*len(data[:,i]) // 100:]\n", "\taverage_last_ss = np.mean(last_portion)\n", "\n", "\tpercent_error = abs(average_last_ss - cas_ending_avg[i]) / abs(cas_ending_avg[i]) * 100\n", "\terrors.append(percent_error)\n", "\n", "\tlabelLines(plt.gca().get_lines(), zorder=2.5)\n", "\tplt.title(\"Per<PERSON>r for final result: \"+ str(percent_error))\n", "\tplt.legend(bbox_to_anchor=(1.15, 1))\n", "\n", "\n", "for idx, i in enumerate(errors):\n", "\tprint(idx+1, i)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_cas_only(cas_directory, pattern):\n", "\t# cas_directory = \"\"\n", "\n", "\tall_files = []\n", "\n", "\tif cas_directory != \"\":\n", "\t\tfor root, dirs, files in os.walk(cas_directory):\n", "\t\t\tfor file in files:\n", "\t\t\t\tif file.endswith('.csv'):\n", "\t\t\t\t\tfile_path = os.path.join(root, file)\n", "\t\t\t\t\tall_files.append(file_path)\n", "\n", "\n", "\tcas_pce = []\n", "\tcas_time = []\n", "\n", "\t# Process each file\n", "\tfor file_path in all_files:\n", "\t\tif pattern in file_path:\n", "\t\t\ttry:\n", "\t\t\t\tdata = np.genfromtxt(\n", "\t\t\t\t\tfile_path,\n", "\t\t\t\t\tdelimiter=',',\n", "\t\t\t\t\tcomments='#',\n", "\t\t\t\t\tskip_header=0,\n", "\t\t\t\t\tencoding='cp1252'  # Specify the correct encoding\n", "\t\t\t\t)\n", "\t\t\t\tpce = (data[:, 1] * data[:, 2] /1000) / (0.1*0.128)*100\n", "\t\t\t\t# data_with_pce= np.column_stack((data, pce))\n", "\t\t\t\tcas_time.append(data[:,0])\n", "\t\t\t\tcas_pce.append(pce)\n", "\t\t\texcept Exception as e:\n", "\t\t\t\tprint(f\"Error processing {file_path}: {e}\")\n", "\n", "\tcas_pce = cas_pce[::-1]\n", "\tcas_time = cas_time[::-1]\n", "\n", "\n", "\tplt.figure(figsize=plot_size)\n", "\tplt.xlabel('Time [sec]')\n", "\tplt.grid()\n", "\n", "\tplt.ylabel('PCE [%]')\n", "\tplt.subplots_adjust(left=0.086,\n", "\t\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\t\tright=0.844,\n", "\t\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\t\thspace=0.2)\n", "\n", "\terrors = []\n", "\n", "\tfor i in range(8):\n", "\t\tif cas_time:\n", "\t\t\tplt.plot(cas_time[i], cas_pce[i], label = \"cas\" + str(i+1))\n", "\t\t\tsecond_half_cas = cas_pce[i][len(cas_pce[i]) // 2:]\n", "\t\t\taverage_second_half_cas = np.mean(second_half_cas)\n", "\t\t\tprint(average_second_half_cas)\n", "\n", "\tprint(np.mean(errors))\n", "\n", "\tlines = plt.gca().get_lines()\n", "\tx_min, x_max = plt.xlim()\n", "\tnum_lines = len(lines)\n", "\txvals = np.linspace(x_min + 0.1 * (x_max - x_min), x_max - 0.1 * (x_max - x_min), num_lines)\n", "\tbold_font = FontProperties(weight='medium')\n", "\tlabelLines(\n", "\t\tlines,\n", "\t\txvals=xvals,\n", "\t\tzorder=2.5,\n", "\t\talign=False,\n", "\t\tfontsize=11,\n", "\t\tfontproperties=bold_font\n", "\t)\n", "\tplt.legend(bbox_to_anchor=(1.15, 1))\n", "plot_cas_only(r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\2024-11-06 --cas vs stability setup long test\\nov6_2024 test 1\\devices\", \"_3_0\")\n", "plot_cas_only(r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\2024-11-06 --cas vs stability setup long test\\nov6_2024 test 1\\devices\", \"_7_0\")"]}], "metadata": {"kernelspec": {"display_name": "stabilitySetup", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 4}