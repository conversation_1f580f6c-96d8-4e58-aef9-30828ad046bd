#include <Adafruit_DS3502.h>

Adafruit_DS3502 ds3502 = Adafruit_DS3502();
/* For this example, make the following connections:
 * DS3502 RH to 5V
 * DS3502 RL to GND
 * DS3502 RW to the pin specified by WIPER_VALUE_PIN
 */

#define WIPER_VALUE_PIN A0
uint32_t counter;

void setup()
{
    Serial.begin(9600);
    // Wait until serial port is opened
    while (!Serial)
    {
        delay(1);
    }

    Serial.println(F("Adafruit DS3502 Test"));

    if (!ds3502.begin())
    {
        Serial.println(F("Couldn't find DS3502 chip"));
        while (1)
            ;
    }
    Serial.println(F("Found DS3502 chip"));
}

void loop()
{
    for (counter = 0; counter < 128; counter += 5)
    {
        ds3502.setWiper(counter);
        float wiper_value = analogRead(WIPER_VALUE_PIN);
        wiper_value *= 5.0;
        wiper_value /= 1024;
        Serial.print(F(wiper_value));
        Serial.println(F(""));
        delay(500);
    }
}


// convert decimal voltage value to 12 bit int to control the MCP4725
uint16_t convert_to_12bit(float val) {
    if (val < 0 or val > 3.3) {
        return 0;
    }
    val = float(val)*4095.0/3.3;
    int bits = floor(val);
    return bits;
}
// void loop()
// {
//     // uint8_t default_value = ds3502.getWiper();

//     // Serial.print(F("Default wiper value: "));
//     // Serial.println(F(default_value));

//     //   float wiper_value = analogRead(WIPER_VALUE_PIN);
//     //   wiper_value *= 5.0;                                           // input voltage
//     //   wiper_value /= 1024;
//     float wiper_value = 100;
//     ds3502.setWiper(wiper_value);
//     Serial.print(F((float)ds3502.getWiper() * 10.0 / 127);
//     Serial.println(F("K Ohms"));

//     Serial.println(F());
//     delay(1000);
// }