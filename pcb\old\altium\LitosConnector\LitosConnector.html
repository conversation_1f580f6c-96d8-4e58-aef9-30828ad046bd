<html>
<head>
<META http-equiv="Content-Type" content="text/html">
<style type="text/css">
		h1, h2, h3, h4, h5, h6 { 
			font-family : segoe ui;
			color : black;
			background-color : #EDE7D9;
			padding: 0.3em;
		}

		h1 {
			font-size: 1.2em;
		}		

		h2 {
			font-size: 1.2em;
		}

		body { 
			font-family : segoe ui;
		}

		td, th {  
			padding: 0.5em;
			text-align : left;
		}
		th { 
			background-color : #EEEEEE;
		
		}
		th.column1, td.column1 { 
			width : 50%; 
		}

        th.column2, td.column2 {
            width : 25%;
        }

		table { 
			width : 100%; 
            font-size: 0.9em;
		}
		.front_matter, .front_matter_column1, .front_matter_column2, .front_matter_column3
		{
			left : 0;
			top : 0;
			padding: 0em;
			padding-top : 0.1em;
			border : 0px solid black;
			width : 100%;
			vertical-align: top;
			text-align: left;
		}

		.front_matter_column1 {
			width : 5em;
			font-weight: bold;
		}

		.front_matter_column2 {
			width: 0.1em;
		}

		.front_matter_column3 {
			width : auto;
		}

		.total_column1, .total_column {
			font-weight : bold;
		}
		.total_column1 {
			text-align : left;
		}
		.warning, .error { 
			color : red;
			font-weight : bold;
		}	
		tr.onmouseout_odd { 
			background-color : #white;
		}
		tr.onmouseout_even { 
			background-color : #FAFAFA;
		}
		tr.onmouseover_odd, tr.onmouseover_even { 
			background-color : #EEEEEE;
		} 
		a:link, a:visited, .q a:link,.q a:active,.q {
			color: #21489e; 
		}
		a:link.callback, a:visited.callback { 
			color: #21489e;
		}
		a:link.customize, a:visited.customize {
			color: #C0C0C0;
			position: absolute; 
			right: 10px;
		}	
		p.contents_level1 {
			font-weight : bold;
			font-size : 110%;
			margin : 0.5em;
		}
		p.contents_level2 {
			position : relative;
			left : 20px;
			margin : 0.5em;
		}
	</style><script type="text/javascript">
		function coordToMils(coord) {
			var number = coord / 10000;
			
			if (number != number.toFixed(3))
				number = number.toFixed(3);
			
			return number + 'mil'
		}

		function coordToMM(coord) {
			var number = 0.0254 * coord / 10000;
			
			if (number != number.toFixed(4))
				number = number.toFixed(4);
			
			return number + 'mm'
		}
	
		function convertCoord(coordNode, units) {
			for (var i = 0; i < coordNode.childNodes.length; i++) {
				coordNode.removeChild(coordNode.childNodes[i]);		
			}

			var coord = coordNode.getAttribute('value');
			if (coord != null) {
				if (units == 'mm') {	
					textNode = document.createTextNode(coordToMM(coord));
					coordNode.appendChild(textNode);
				} else if (units == 'mil') {
					textNode = document.createTextNode(coordToMils(coord));		
					coordNode.appendChild(textNode);	
				}
			}
		}
	
		function convertUnits(unitNode, units) {
			for (var i = 0; i < unitNode.childNodes.length; i++) {
				unitNode.removeChild(unitNode.childNodes[i]);		
			}
		
			textNode = document.createTextNode(units); 
			unitNode.appendChild(textNode);
		}
	
		function changeUnits(radio_input, units) {
			if (radio_input.checked) {
			
				var elements = document.getElementsByName('coordinate');
				if (elements) {
					for (var i = 0; i < elements.length; i++) {
						convertCoord(elements[i], units);
					}
				}
	
				var elements = document.getElementsByName('units');
				if (elements) {
					for (var i = 0; i < elements.length; i++) {
						convertUnits(elements[i], units);
					}
				}
			}
		}	
	</script><title>Board Information Report</title>
</head>
<body onload="document.getElementById('radio_mm').click()"><img ALT="Altium" src="
			file://C:\Users\<USER>\Documents\Altium\AD22\Templates\AD_logo.png
		"><a href="dxpprocess://Client:SetupPreferences?Server=PCB|PageName=Reports" class="customize"><acronym title="dxpprocess://Client:SetupPreferences?Server=PCB|PageName=Reports">customize</acronym></a><h1>Board Information Report</h1>
<table class="front_matter">
<tr class="front_matter">
<td class="front_matter_column1">Date:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3">3/11/2023</td>
</tr>
<tr class="front_matter">
<td class="front_matter_column1">Time:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3">11:18:13 PM</td>
</tr>
<tr class="front_matter">
<td class="front_matter_column1">Elapsed Time:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3">00:00:00</td>
</tr>
<tr class="front_matter">
<td class="front_matter_column1">Filename:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3"><a href="file:///C:\Users\<USER>\Dropbox\code\Stability-Setup\pcb\altium\LitosConnector\LitosConnector.PcbDoc" class="file"><acronym title="C:\Users\<USER>\Dropbox\code\Stability-Setup\pcb\altium\LitosConnector\LitosConnector.PcbDoc">C:\Users\<USER>\Dropbox\code\Stability-Setup\pcb\altium\LitosConnector\LitosConnector.PcbDoc</acronym></a></td>
</tr>
<tr class="front_matter">
<td class="front_matter_column1">Units:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3">
<form><input type="radio" name="radio_units" id="radio_mm" onclick="changeUnits(this, 'mm')">mm<input type="radio" name="radio_units" id="radio_mil" onclick="changeUnits(this, 'mil')">mils</form>
</td>
</tr>
</table><a name="IDIMXSROBU0RNQIMBZAUN4AUIRBPF4EFZYIF03BUOLO2J22EJHK3YL"><h2>Contents</h2></a><p class="contents_level1"><a href="#IDIMXSROBU0RNQIMBZAUN4AUIRBPF4EFZYIF03BUOLO2J22EJHK3YL">Contents</a></p>
<p class="contents_level1"><a href="#ID0RTSGVLGL0E5DQXC3QWYADYS4FHHS4TJPND0M2OJLQGY5OAQM2WM">Board Specification</a></p>
<p class="contents_level2"><a href="#ID5DTS4F2CYZXYMO0GAI1435DNQEFVBYDX0KYNYSMMQO1S3WT5IERB">General<br></a></p><br><a name="ID0RTSGVLGL0E5DQXC3QWYADYS4FHHS4TJPND0M2OJLQGY5OAQM2WM"><h2>Board Specification</h2></a><a name="ID5DTS4F2CYZXYMO0GAI1435DNQEFVBYDX0KYNYSMMQO1S3WT5IERB"><table>
<tr>
<th style="text-align : left" colspan="2" class="">General</th>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1">Board Size</td>
<td class="column2"><a name="coordinate" value="20866142">53mm
			</a> &nbsp;x &nbsp;<a name="coordinate" value="63188976">160.5mm
			</a></td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1">Components on board</td>
<td class="column2">7</td>
</tr>
</table></a><hr color="#EEEEEE"><a href="#top" style="font-size: 0.9em">Back to top</a><br><br></body>
</html>
