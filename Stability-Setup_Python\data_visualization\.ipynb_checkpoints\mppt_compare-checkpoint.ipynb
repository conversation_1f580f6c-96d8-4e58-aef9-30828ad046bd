{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b9b9b78dccb64da082c820822d65f404", "version_major": 2, "version_minor": 0}, "image/png": "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****************************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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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****************************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' width=1000.0/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "733cc0e020094c328ff6954661dc5258", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=1000.0/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "20db5ab336c7419e8ca36ca5d4d33841", "version_major": 2, "version_minor": 0}, "image/png": "iVBORw0KGgoAAAANSUhEUgAAA+gAAAGQCAYAAAA9TUphAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAgxpJREFUeJzt3Xd4VGX6N/DvtPTeExJCAqGE3nvv2FAXC66KIjbUXcHVxYa6rih2fVlZV0Hdn4V1LWsXBASUIh3pvaUC6X0yc94/JnNmzsyZkmQy5yT5fq5LyZz6zDyZzNxPuR+NIAgCiIiIiIiIiEhRWqULQEREREREREQM0ImIiIiIiIhUgQE6ERERERERkQowQCciIiIiIiJSAQboRERERERERCrAAJ2IiIiIiIhIBRigExEREREREakAA3QiIiIiIiIiFWCATkRERERERKQCDNCJiIiIiIiIVIABOhEREREREZEKMEAnIiIiIiIiUgEG6EREREREREQqwACdiIiIiIiISAUYoBMRERERERGpAAN0IiIiIiIiIhVggE5ERERERESkAgzQiYiIiIiIiFSAAToRERERERGRCjBAJyIiIiIiIlIBBuhEREREREREKsAAnYiIiIiIiEgFGKATERERERERqQADdCIiIiIiIiIVYIBOREREREREpAIM0ImIiIiIiIhUgAE6ERERERERkQrolS5AW2Q2m5Gbm4vw8HBoNBqli0NERERERE0gCALKy8uRkpICrZZ9m9TyGKC3gNzcXKSlpSldDCIiIiIi8oFz584hNTVV6WJQO8AAvQWEh4cDsLyRIyIiFCuH0WjE6tWrMWXKFBgMBsXKQawLtWA9qAfrQh1YD+rBulAH1oN6qKUuysrKkJaWJn6/J2ppDNBbgHVYe0REhOIBekhICCIiIvghozDWhTqwHtSDdaEOrAf1YF2oA+tBPdRWF5y2Sv7CiRREREREREREKsAAnYiIiIiIiEgFGKATtUI1FUZUldUpXQwiIiIiIvIhzkEnaqTcY8U4sfsCNFoNhlyegYAg372Nyotq8NPKgzDWmjBwejo6Zsci/2Qp6qrrUVNpRNmlGpw/VITCM+WS80KjAjFzQX+s+ttvqDeaJft0ei36jE9F4ZkyZI9KQWb/eJRdqEF0cog4n6q2uh4FJ0uR2iMGWm3rnmNVUlCFwFA9AkMM4nMRzAL2rD2HvBPFEBIVLiC1KnU19T59jxMRNZfJZILRaFS6GC3OaDRCr9ejpqYGJpOpxe5jMBig0+la7PpEjcVvHdTu7d+Yg91rzmLK7T2RmOE6qV9NpRHvLtwk2bb3p3OITg5FcV4l/vDIILfne1JTacQHj24WH//wz/1en1tZUosPn9wqu89Ub8buNWcBADlHSxCTEoqi3ErEpYVh+t29EREbjF8/PYZDm/MAALc8NwLhMUGy1zKbBVSV1iEsOtDrsnliMppRcKYM8R3DYQjQ4diOApzccwFjb+yGoFDnpDCCIKA4rwpfvrYb1WV1iE4KwZArMtFlYALKLlbjw8WW1yEsOhDXPToYweEBWPd/h3G44fkB4Xj7x02Y99oYp8DrwKYcbFx1FOZ6ARqtBh17xmDAlI5IyYoGABTlVWLXD2cQ3zEcPcekQG/w/IFeX2fCid0XkNYjBiERAc17sWRUldUhONwgSV5TlFuJ6oo6dOga7fP7eVJdUYfjOwrRY0Qy9AH++cJTX2fCpk+P4eCmXFz5QD8kZlreh4ZAHcxmATqddLCYqd6M3avP4PyRErEBa9ePZ5B7rATT7+6NtB4x+HbZXuQcLQEA9BzTAf0mpsFYa0Jcahg0XjZiCYKA/BOlCA4PQFRiCARBEOvp4C+52P7dKRgCdBh+dWd06BaNM/svISUrCgHBepw9cAn71p1H7rESXPfYYJzedxERsUHoNizZdy8cEbUqgiAgPz8fJSUlShfFLwRBQFJSEs6dO9fiCdqioqKQlJTERHCkCgzQqV3a+cNpbP3ypGTb9m9P4fL7+kq+RFsVninDp0t2yF6rOK8SAPDfFyz7b10y0imAFQQBgmB7XG80QafXivepN5qcgv+WUpRrKe/FcxX492NbMGvRIDE4BywNFsNndsaZ/Zfwzf/bK3uNPuNT0XdSGgKC9Pi/J7agtqoeI//QBf0mdQRgCboP/pqLi+crcPCXXHQZmIDJc3s69c5XltbivUd+FR8PmtEJO747DQAIDgvAmBu6QhAErP+/wzi5+wJqq+qdylKcX4Uf/7UfP/5Lur2iuBYr/vIL7l42zi44t/nXnzdKHg+9MgPbvjolPhbMAs78fglnfr9kKU9EAKobphUc2ZaPXz49hjE3dEXvcfJroppNZmz/7jR2fHta3JY1OBGn9l1Efa2lJ6BDt2jkHCnG7S+NQlCoJcjOPVaM9f93BKOvy0LHnrGy1waA2ioj3llg+53pOToF427qjtoqIz5+ZhsAYMgVGRh8WYbkPEEQUF1udArqvXVyzwVs/OQoBs3ohIjYIGz58gQ6dIvGyGu7oL7OjBUP/QIAOLajAN2GJmHzZ8cx454+6NCteY0FVWV1OLGrEDWVRhTnVaK4oAplF6pRVyPtVfnqjT2SxyERAbjhiSEIDg+AIAg4e6AIp/ZdxIGNOeIxZw9cEn/+fvnvTvc+sDFHcnz3YUlIzIxEVVkdju8oQIdu0Rh6ZSYAQGfQ4u0HNkCn12LMDV2x/v8OAwCufKAffnr/IHqN6YDfvj4luf53b9numZIVhbi0MOxbd17c9p+/bxd/LsqrwrCrMr1uJCCitsManCckJCAkJKTNB5NmsxkVFRUICwuDVtsys3IFQUBVVRUKCwsBAMnJbAQl5WkEwT5sIF8oKytDZGQkSktLFV9m7bvvvsOMGTNUsTyFK8ZaE8xmAYHBeuSfKsWRrfnoPS4VMcmhPr9XXXU9fv3sOA7+kuu0r0PXKAy5IgPf/3M/Rs3KQrehSRDMAqorjFj58C+SY3V6LUz1ZqdrWF1xf1+kZcegsqQOOr0G3761DxfzSnD7C2NQV2nG+4ssPeXzl09AvdGEf96/QTxXo9VgzPVZOLQ5D+XFtWJQaNkJ9J/UEUOvzETZpWqc2X8JpnqzpLFh7kujUV1Rh+gky+tnNpkhAPj534dxeGt+U142r42+PgvV5UZodRqnIAQArnqwP1LtArVdP57Bli9OyF4rPDYINz45FPs35mDzZ8dbrMzNERkfjD/+bbhkm2AWAA2w6ZOj+H1DjosznUUnhaA4v0qyLS4tDJfd21e2wecf96z36rqxHcJwwxNDAAD5p0qx9r1DKCmoQkRcEG5+doRX1xDMAr7+f3tx7mCRy2Om3NETq9854HL/uJu6IalzJGJTwgBI/z4J9Rr8+tlx5B0vQUhEADp0i0Zmv3j8+M4B1FYZUVXavHwLSZmR0Bm0yDlS3KzrNEdwuAHV5b4ZkpqSFYWZC/r75Mt5a/mcaA9YFzY1lUaYTUKLjDryRK31YDKZcPToUSQkJCA21nXjbVtiNptRVlaGiIiIFgvQrS5duoTCwkJ07drVabi7Wr7XU/vBHnRS1MFfc7H+34fFx5HxwSi9UI39DYFN/8kdMeLaLs2+jyAI2PjJUfG6ckovVOOXT4+jpsKIn1YeRLehSfj5w8M4+Ku099U6BLy8qAZr3j2AiLhgHNkmDXy/fnMvxs7uhg0fHbHbqsW/HpAG+ju+O4WMvvGSbQOnpaPX2FT0GptqGf59ugxJmRHQOgzTjU4KFYPwvBOlOPP7JQybmYmgMAOCwmxfKqzn+SOp3KZVx9zu/9+ru3HPP8ZDq9VAMAsug3MAKL9Ug7f/tMFpe0CwHl0GJaD32FREJQYj/0Qp/vfaHq/KF5MSiqAe+chdG+7x2Hv/MR7/uNc5CL7luRGoLq/Dp0t2oPRCNUoKqlBZUottX5/EoOmd8M2yfZYgvZEcg3PAMsrh/UW/YsodPdG5f7xYl0d/K/D6updyKrD921MoyqvE8R2F4vayizU4+EsuskeleLzG/o05boNzAG6DcwD4+cMjkse3vTQCxnItzu4vwg//tJ1bnF+FnKMlsg087lz5p36WYeK/X8ShzXk4tfeiuC//ZKnsOYEheoy9sRtO7rmAUbOyUHqhGl+8vEvcr9EAvmrCdhWc3/T0MLHhxJOwmEBUFNUi91gJ1v37MCbe0sM3haNWwWwWkHO4GBHxQQiLCXKauqFImUxmaLQan/bkms0CPl2yHbXV9bjxyaEIjfTdlKrWzDrnPCQkROGStE3W19VoNHI+OimOATop5tt/7MPpfRcl20ovVEse715zFvHp4cga5Dqzl8lkRkl+FWJSQl1+SXjr3vUev2hXFNeiorhWfHx8Z6FTcH7TM8PE+dnhMUG45i8DAQDdhyfht69PIe+ELRCQBufytn0lPQcA9AG2L106gxYpWVEer3P5fPmh+fYGTu+Esx6CLE9SsqKQe6zE6+N7jemA6gojTuyyBYb71p3DxXMVOLbd+yDT3rxXx0gep3aPQZ8JqZIhwa6MvSkL2/fn4843R4s9I3t+Ootf/+vcQy83hPi2paMQEhEgmaNvnfMOWBpmvJHaPRrFeZWo9LJn2D747TIoQRJoX/foYGz4+AgKTpW5PN9VsHvuUJEYoFeX1yHnaAky+8VBMANanUZ8DTZ+ctSrcjbGyoc2AwjFD7+4D+ztXf3QAFQW1yKhUwQi44PF7fa/+xl945HRNx5FuZXY+MkRcR65VbehSWKD2rQ7eyG1ewyyBlv+voRGBeKPfxuO/3tiCwDgmr8MxGdLdwIAtHoN7n5znOzIBa1WA3MTGmUAwBCkQ/dhyeg6JAlvyTQIBYbqccfLtt/5z1/cibwTpTi8OQ+jr8tiArs2zlhrwn+e246SAmkDXlqPaFz5p/4KlcoSmL81/2cAQOcBCZh2Zy+nYwSz0KSpGEW5lSi7WAMA2L8hR5w+QhZtfVi7Uvi6kprwk518prK0Fjq9Vjaxl1XZxWp8umQHaqvrve5lPLv/kssAXRAEvPPgRtTXmTFgWjqGz+zsdMyFs+WS4Nza++3Jj/+yJWkLjgjA7MVDXT631O4xSO0e4zLYc+fsAWnQ7E3iMTmePlxSsqJwzUMD8PlLu9weZ3XvP8ZDo9Vg9bsHxGD6ygf6Yfn9P0vLG6hDfGqYU0PDpNuy0W1okvh42d3rAMDj6+NqqLRGq8Gg6emy54y+ritGXNsFy+dLy/aHRwaJuQEAS9IwR0mZkS7LctPTw/DD2/vRZ0IqMvvFu/3ddmfENV2w+XPL877zjbEw2CVPO3+kGP97dbfX17IPzq9/fDDiUsOR2j3aKUCftWiQy7wJViWFti/9n7+0SxIEZA1KwJQ7eqGuRjrvf8gVGTi8JU/8Au1oxr19kJAeLskt4A2dQYsr/9QPEbHB+P6fv6PwdBk694/HhXPl6DokCUOuyHD5Oy63PSYlFDMXDMCl3Ap88sxv4vYJt3RHStcohMcEIbV7jNN5EXFB6DW2A3Q6LRIzIjB/+QRJA8Do67OwadUxdO4fj9E3dEVoZCDMZkE2uPaG9XfS1eoJtZXS17/78GTxvXZi1wX0GMH5km1VdXkdVvzlF9l95w4Vo7q8DsHh/h8CDgBHttkaWK0NsPV1JlSV1SEiLhjrPzyM0/suWvI/hDWujAWnbJ8lNZVtP1M5EZEjBujkEyf3XBCTK9315lgIZsvwUMcszscbkjzZ0wdokdkvXhy2mz0yGeNu6i5m3q4oqYUr9r1Zu344gz7jUhEaJR0Od3irrRd8zgsjERoZiO3fnoJGA8SnR+AbD72efSelYdQfstweIx47MQ3Hdxa67c30RGdouWGLIW6GCoZGBkh6dK09HxfP2ZZ0kyvb7MVDceFsOfJOWOp/4PR0ZI9MQURcsOS4m54ZJptpftCMTuKc9R4jkpE1KBF7157DhXPluO7RweKcZU90Oi2uXtgfX7xsCXZ7j0tFYkYEZtzbB9/9Yx8AS2+lI3cBelRiiDh/21H/KR2xe/VZj+W64v6+6NAtGtUVdUjvFSsJzi33l5/P1qlPnNMIE3sDpnZEXKplqH7n/gnY+f0ZAJYAuseIZIRFy2fin798AnKPleCLl3ehvs4MQRCw9v1DTj10x3YUIufYL05TMAZfloHU7jH4/MWdDY87YXtDIrzeYzsgo08caqttQaW1ocZ6T1du+fsIcb7prL8OcnlcY8WmhGH8zd2x/t+H0XlAArQ6LbJHuh7Wr9FoMPbGbk7brPqMT0Of8WmS/fbBdUCwHnXVzskMXXH8fWiMdR8cwrlDRZh0W3arXx6xtRIEAXU1JhgCdU2qg4riGmz46Aj6TuqIxEzp3zpXSTqtSi9UOwXoO747hdCooBZtuBEEAes+cJ6S8cnffkPphWpc+/BAHNxkyfNyZv8ldBuahJKCKvy+/jxMJgHjZndz27N+Zr8taSOYJYmI2iEG6OQT9pmPt/3vJPb8dA6xqWG4/tHBkg/iLZ9L5xzHpobhhseHQDALGDAtHfV1ZiSkh0Oj0aDHiGQc3pyH0kLpsHcrufyGp3+/iJ6jO0i2nT9sSQw1+fZscS6bNbO1IAi45bkRKLtYjS9fke/FTG1E9mmNRoNBMzrh22X73B539cIB+PnDw7LzjnX6lvuiHRRqe8sbgnQw2mXATukaLTvs3LGMnfvH48TuC+LjsOhAhMcEodeYDsg7UYI+49NkE/tEJcjPmwuPDUL2yBQMmJIuNgBc9WB/GGtMjU4QlJIVjWEzM3FsRyEGX94JAJDeMwbdhiUhIEjv8nr3vjUe5w8VI+doMXb+cAbDr3YeieGo78Q0jwH61QsHiFMURlwjn0tBb9Dhnn+MR22lEdXltgzsWYMSXAboWYMTMfxq2/XiO4bjhieGICwmCIHBrv+sx3e0BPQ6veV1LimowsVzFTjiInlgVWmdJHu5VVJmBJK7RCIwWI+eYzqIAbr1va7X2xpyghvyIThO1YhLCwMSCzFsxFCkZ0sbAXwte2QKUrtFIySy5Xsb41LDUHaxWjJdxp3GDgGuN0qz1h/bXoD0XrGS0SrkH4e35GHt+5ZAdcDUdK/+bjj6+cMjOLP/Ek7/fgl3vjlasq/0ou2zb9BlnSQrQgBwagi6cLZcXImiJQN060obVnFploYF6xS133+2TTda+94hp/wK5Zeqcdm9fV02RlfaNcozPm8bLly4gCeffBLffvstCgoKEB0djb59++LJJ5/EyJEj0alTJ5w5cwYff/wxbrjhBsm5w4cPx+HDh7Fy5UrMmTMHALB371488cQT2Lp1K8rKypCUlIShQ4fizTffREJCAgD5kVVy1ydSIwbo5HN7fjoHALh0vgLHdhQgNCoQkfEhqK8zOR1r7b3UaDVOPaXWgK68qAaVpbVicL3pP0dRV1WPkbOce7V//vAI0rJjEBEbDEEQ8NFT28SewcQM555SjUaD8Jggp153e42d4xngJkCySsmKQu9xqeLc3jtfHysmRDMEttzb0v65TLuzF75+w9ZDY6x1rh8AYu/jkCssjRpT5vXC5s+OY+9aSz1bPwTHzu4me7693mM7OGU27zLQ8mFq/2XNEKBrcs/iwGmdMHBaJ/GxVqfFpDnZAGxJdhxpNBqkZccgLTsGvcamIjTKcyDnLnHRHa+OgV6v9Xo0hFarQXB4AILDLUvLhccGIb1XLNasPCh+Q+3QLRpX/akfLp6vQEyK8woHsR08jzS4+qEBAACdwfbF5T/PbXd1uJOBDVMMNBoNrnnIkn/BvqGsqtwyAkNr18gU7WI1hrjUMFRE5XmVY8EXHEd0+Jo+QIv6OjM69oxBRXGt24SUVmNu6Nro+3TMjgUgTcb408qD6DokscXnUApmAb99cwpmk1nSQNRWVJXVIe94CTL7x3v1Wu5eY2ug2/Wjdw17js4ddp0XJDIuGIWV5bjs3j5OiUgB5+C1urzlE4ECztOyHJmMrlc4ASzD85ff/zOuXtgfKVnODeCSzyJBQF1NPfZvzEFmv3iXDb2kbtdeey3q6urw/vvvIzMzEwUFBVi7di0uXbKNlkhLS8PKlSslAfTWrVtRUFCA0FDb58iFCxcwceJEXH755fjxxx8RFRWF06dP46uvvkJlZaXkvitXrsS0adPEx1FRUS33JIl8iAE6NZvjHFV7a1YcdHtumJvAOCQiAFGJISgpqELBqTJk9otHRXGtmAws2e6L/bCZmeJSY/9+bAsSMyIw9sZuYnBuaSRw/QVdq9XgludGYPU7+5F/Ujo8Xadv3JDzAJlh1Pam390bABCfbsskrtXZvgyGtmAvn0arwW1LR8FsEhAYIn37B4fb5lfbB9vZI1OQ3itW7H3WajUYPrMz9AFapPeKa9T9R12XJQnQrfPc1cRxSbPGuvaRgW57sT2xX1f96oUD8EVDzoDL7+sDjVYj9oJ7o2N2DM4eLMKgGZ0w+PIMcQiuq9/pyIRg/PGZ4agqq3NaWnDYzExJw4eVfSBTX2cWt131536orzNLEupZG3sAoOfYFGzb6z7rf2ty45NDcf5wMboNS4Kp3uxVgO5NA45jA0dUYojMChGWxFreNNI0lclkluR46Do0yevpJ62BIAji7/ykOT3QbZjnHuiiXGkwUFtlRGBI4/JUWN4/8v3Exob3kz5QJ/k9EFcXcDjNbPJPf7P18yo8Ngjll2qcErDWu1mC1N6Gj4/ihieGSP6G1FYZJaO2BFhWkdjy+Qls+fwE5i+f0Ozyk3+VlJRg06ZN+PnnnzF27FgAQHp6OoYMkU4fu+mmm/Dqq6/i3LlzSEuzTCNauXIlZs2ahVWrVonH/frrrygtLcU777wDvd7yWZuRkYHx48c73TsqKgpJSRxdRK2P8mt0UKvnanisK1mDEsSfPX1Btbakf7/8dxzfWYicI7aWe/vl2fpOlM4JLThVJukZ7DtBul9OeEwQrn14kNO17INnb7j7gtapTywy+1mG8yZlROLKP/XDzc8Ol9wjNrVlv/SGRAQgLDoQhkAdbnpmGMbO7obM/vEYcnkmLru3D/pOTEP2SOmX09DIQMmXKJ1Bi2FXdUZyZ9fzt+VodVrMeX4kkjIjMOWOnqoLzn0hMd13a6SmdInCTc8Mw11vjm1S8sAZ9/bBjU8OxZArMiTzY+UC9AFT0zF78VAAlt+R25aOkuzvN7mjx/uZ7IZfp3aPQac+0gac7JEpmPfaGNz+0ijEdpDvWW+tIuKCkT0qBTq9FgFBeq+Wr9N7EaBPvaOn0za5ERTllzwnvmwO+5UYAOC/S3Zg/b8PYdXff8OxHU1bkUFN7FcQOSUztaSiuFayVKVc1v7iAucpS56466ivb/j80wdo0W+S7XPJVX4JfwXo1kZ562eZY0OBqx70nqOl74mi3Eq8v2izZNqG/agE67VzjpQ0q7xtlSAIMNaaFPlPboqhK2FhYQgLC8OXX36J2lrXU38SExMxdepUvP/++wCAqqoq/Oc//8Ef//hHyXFJSUmor6/HF1984bEc8+fPR1xcHIYMGYIVK1Y0qtxESmIPOjXLpZwKyRJM1ozH5w4WuVxyasC0dBxryETtaRSh/Vy0H/+1H8Nmyi+34il46Tsx1e1+d8JiGtej6q4H3PGLS1oPWxbp218cBbNZ8OuySVEJIYhKCEGvMZZ5+2HRgU5Bla+FRgXi2od9lwRMSYMvz8D2b04htkOomJjJ140OzRnSqdNrZYM5uQB96JUZ4jrrgCVI/8NfB+F/r+3G9Lt7e7XmsrskhFbW329X0w3aitHXZSG9VyxSu0fjX3/eKHtMeq9Yt9eYdFu2bK+43PQPk8m7Xktv1VYZ8fvPOZaM9gYt1rwrHQ1VbzSLy1CufucAOvePl/z+tDZldvO9y4tqsfIRS4LEcbO7wVhnwvuLLKsSJHeJxMRbe6CkwDk3SklBFZJkplK5424ovTVwNQToEBCkx5S5lsaa3WvOoryoxinYsG80aOoSZ55UldWhpCEvjNyqGIB8fhjAMhJg0pwe+MluTnplSS0+f3EXdHoNrvpzf5jqpecKckMFCIBlxJJ1apy/3fn6WJf170iv1+O9997DvHnzsHz5cgwYMABjx47FDTfcgD59+kiOvf3227Fw4UI89thj+O9//4vOnTujd+/ekmOGDRuGRx99FLNnz8bdd9+NIUOGYMKECbjllluQmGhb8eeZZ57BhAkTEBISgtWrV+Pee+9FRUUFHnjggea/AEQtjAE6Nct3b9mSodnPT021Czzt6QN1iEpsesBRKZN4yTqE9sYnh2L7d6cky1ABQI+RyY364uiYFKuxS8S4u1eP0a6HTSq1XA413ZDLMzDk8gyli9FojiNX7nxjrOzvbWKnCNz52liP17v8/r44sDGnSXNw2yp9gM7Ww9hgxLVdEBZtmW6T4MVIC1eJ3+RGHvm69/TX/x7Hoc152PbVSUTEyffY2ju97xIy+7dssj9fEQQBBzbmICYlVJwDbU0mCgCFpy3TnA5szEFMcijsA8S846X4vyekq1FEJgSjtLDaaSUEb7hrpLaOILMGQlmDLcHHnp/kk1Oa7RppzIIAHXwboJtMZny6ZLvYs28L0Lz73dNoNOg2LBkZfePxrwdtjVYXzlpWCjmx+wJMjrlqBDgNoW/rzGYBgklo0RVd/O3aa6/FZZddhk2bNmHr1q34/vvvsXTpUrzzzjti4jcAuOyyy3DXXXdh48aNWLFiBW677TbZ6/3973/HggULsG7dOmzbtg3Lly/Hc889h40bN4oB/RNPPCEe379/f1RWVuLFF19kgE6tAgN0arKSgirJWsj2PQFarQYde8bg7IEiRCeFYNCMTijOr0K3oUmS3rvGfvA6JhgDgJkL+gOwDPucekcvjL3RiHcXbhL3j73Bc/Iye4kZEcg5WtK4gjnoMSIZhzbblneb+8pIfPP5anTq7b7HjMgfHKdtNGepLwBI7xmL9J783XZl5oL+OHvgEvqMT/U6p4W7wM1+Xr+VrwN0+79frta8t3did2GrCdBP7r6ADR9bRn7d/eY4aLRwuSLDplVHZbfb6zokCdu/OYWaSu+X17Ny1cstmAUxp4PjcqWu2P8OCGYBaN7b2klFUa1kdQJrgC4IQP5J29rlruJ16+90QLAe2SOTxREYVmaTWZxGEBoViMqSWsul2lGELggC3rrXsnzsbUtHuV3JRB+gxZ2ve25AbQn6gMY3HgQFBWHy5MmYPHkynnjiCdxxxx1YvHixJEDX6/W4+eabsXjxYmzbtg2fffaZy+vFxsZi1qxZmDVrFp577jn0798fL730kjhE3tHQoUPxt7/9DbW1tQgMbF6uGaKW1naa58ivinIr8eFiaS9CdJK0Z/yK+/th/vIJmP3UMHQdkoShV2YiKjFEEsh7mg903aODPZYlPFb6ZdU++AgKNTS6FXqk3ZrnV/65X6POlSvT1Qv7Q2fQwhDu2yGoRE1lP/d53qtjFCxJ+9ChazSGX92lUQknZy1y/bdPbmjp4S15Mkc2zZkDl2S3x6WFidNhHB39rfXMQy88Uy7+nHeyFEV5je/5tuo7IU2sD2Nt4wN0V53c9onWXAZDjkniJEPcG18UTxynaNknRN0vsxyjI/tGp3E3dZcdTWddwlMMTAXBKT4/uecC8k6Uoi26eL5C/Pn07/LLbFppNBoYAnWK/OeLFSOys7Odsq4DlmHuGzZswFVXXYXoaO+WuQ0ICEDnzp1lr2e1Z88eREdHMzinVoE96G3YxXMVqDhjwMXzFUjO8H4tb2/sWSvtbRg7u5s4/K5RPDSMx3cMx/zlE7Ds7nUuj3H8oLDvkQhu5Dra1nsOmtEJoVGBSOsuP1TfE/svFMldolBf34QvbkQtRKvT4vrHh8BsMnu1LCD516AZnTxm6588NxtbvzwpJoezH6LdXN+4yB9y8VwFOvdPkN0HAL9+dhzHthfgpmeGNXtURku6eN4WoP/v1d2SffZLQYZGBqCy1P3SZRn94sSs48Ya+aUq3XEV6FiHkQMyI1waznH8+LTP2XIppwJh0UHNXpXCnskhO3ujlwS1e64arcap4aG63JaXIjw2yDL0XZA25BfnV+L75b8DAOa9NsavOVv8odxutIq/ls1raZcuXcKsWbNw++23o0+fPggPD8eOHTuwdOlSXHXVVU7H9+jRAxcvXkRIiPx0yG+++QaffPIJbrjhBnTt2hWCIODrr7/Gd999h5UrVwIAvv76axQUFGDYsGEICgrCmjVr8Nxzz+Ghhx5q0edK5Ctt6y8bSfz+cw5KDgbhbEaRzwP0i+cqJI9d9aq0tMvm93HaZp+tWpDJtOuNoVfKJ6Pzlv0yWy29NjFRU8S18GoB1HhX3N8XJ3ZfwIBp6R6P7To4CV0HJ7ltvGwKd/OoQyIC3A5t3dOQgfvtBzZ4HJ6rhPKiGnzw6GaX+wOCdBh8eQZCogLRY3gyNq46ipMNPbquz9Hb9aA3JUCX3y5eS+M8DF7unLMHL+G3r0+Jjz9buhOAb5eyrHeYH24/xN3+Dq4Gxnn6KLRfKSAxIwInd1+AAOlogDK71Qo+W7oTs/46yOspAGpWbzTht69PSaZabP3ypOzSlq1NWFgYhg4dildffRUnTpyA0WhEWloa5s2bh0cffVT2nNhYy5Qps9l5KEh2djZCQkKwcOFCnDt3DoGBgcjKysI777yDm2++GQBgMBiwbNkyPPjggxAEAV26dMErr7yCefPmtdwTJfIhBuhtWHSypfXRca3W5jLWmcSkLgBwxQN9m3wtb5e8iE4KkayNCgBT5vZEp97OGcftA3T7ZXH8KblL4zL5EhF17BmLjo2cy28/l/dSTkWz10L/5b+u16bvMigBPUd3wK//PQ4A6D4iGYmdIpzWZAeAlQ//gjteGd3odcGtco+VIDw2SHa+fVN98sw2t/snz+2J4PAADJreCQBkGxi6j0jGYbv5+YYgnRioOgaw3nA1FP3coYYlRd19RNp9fn79hvyoh5LCKkQn+WZJQ8f1zTWNXILUU2O1depBfMdwaKwhv0MPuqnOVoai3Eoc2JTrtDRqa7Tz+zOyeRA+f2knZi4YIPle09oEBgZiyZIlWLJkictjTp8+7fYaRUVF0GotjYOZmZl4++233R4/bdo0TJs2rdFlJVILzkFvwyIa5kH7Okgts1sv9o9/G46O2Y1PDmWdF57mItu7o6v+3F/y+NpHBqLLQPmhlva9BX0mNH15teZISI/AuJu64fL7m954QUTkSZdBtqlFn/ztt2Zdq666HjlH3AyVFyy9ptPu6oUxN3TFxFt6uB09dSlHOtLq4vkK/Piv/R4D2bzjJfji5V342ENA3Vh1boagB4bqnRp8rYG61cDp6Zjwx+6SbQFBehiCmt6D7qqR2pteb2/at5uSuM4VxznoYgm9bGj3djCZPkArXtzx9REcWixqq9rGco07vjstuz3veCnOWxtriKjdYA96G2ZdG9zxQ7W5LuVavnQFBOkQGR/cpGvcumQEKopqPc6ztAqNks6jS+wU4dXQcSXnp/UcrcywfyJqP7xdi9gb3721T8wcLiehk2VpOHfz0O3VVEiDw1XPWhoQju8sxPzlE1yed2KXZVh5U+Z0uxMeE4TyIssQ6bveGAudXot/NGTMrpUJZEOjAiUjFAZO7wSNVoNOvWNx+ndLIr2AYF2zhri7Yp2eldHXeZSY3EdfcLhBMofbqkmJ61xw+13Cm+Dbywhdq9WIhwqCwygDh7aAlljr3d9KCqWjA3uN7YD9divW1FS2jUYIIvIee9DbMGsvdb2PA/Q17x4EAAQ1Y93u4LAAr4NzOZ6Cc+s8Scd1iImI2hL7+bfNTQhmv7zkxFt7iD9PmdsT42/ujq4uEoGOvbErADgNw62ttgUWOUelPfPuej5bIiCpq6kXg/MbFw+FPkAnCe6GX91Z9rwxN3RDQLAeXYckisnaOnSz5XTR6bXi9ib1oLvIk2JdMs1xSURXXA2B9mWjgVO9NDI2dvzYdvU5nnO0xOVqL21xxbWaCunr2tlhuUL7pe2IqH1odwH6kiVLMHjwYISHhyMhIQEzZ87EkSPS+XM1NTWYP38+YmNjERYWhmuvvRYFBa1nCRkra5Dq6wDdyn6ouz8MvqyT18fe8MRQ3PDEkCb38BMRtQaGQNvHeHMy8jsGihF2fztjO4Qhe2SKy97KXmNTMX/5BMxcOECyva7aFhyu++CQZJ+rpdwA4Mi2fK/L7S37Hkn7ueUTb+2B7sOS0HeS/DxmnUGLea+OweTbe4rb7Jczsy51BTStx99VvCkG6LKvuW1+tlV1Q/AcFCad8+/LAN1+ulx8x3BJLze8WD61SQlTPUTkbSEJa221dJSD4+ozZRf9+12LiJTX7gL0DRs2YP78+di6dSvWrFkDo9GIKVOmSNZOfPDBB/H111/j008/xYYNG5Cbm4trrrlGwVI3jdiD3oTENXLMJrNknlSn3o2fe94cnQdYhlVGxHlOGhQZH9zsZElERGpnv7Z6cxKCOgYJMXaJxbztxXXM8G6/7Jcj++Ddkf1IAG8TiXpi37gQGGJryOg+PBkT52RDp/P+65DjsmfWOej1RrMkePeKi8Ot19HKlEsMjO1OFhoC+uyRKZJjqzwsE9cY1kaD4HADrnqwv4ejnWka843Trg3CvvHI8ffh1L6L+GbZ3lbdy2yfqyEsOhBR8dLlxQ5sypU89tV7gqT4upKatLs56D/88IPk8XvvvYeEhATs3LkTY8aMQWlpKd5991189NFHmDDBMkdu5cqV6NGjB7Zu3Yphw4YpUewm0TcE6L6ag35gUy62fXVSfDz8mi4+ua63YjuE4eZnhyO4GUPriYjaEscs6Waz0KSMz5V2Ac6Vf+onNvAC3gfojiOWdq85i+HXdIZGoxGH4uv0WpjqzV4nLy0trEZUovx6yI1hNlk+B7sPT2p2r2v34ck4vrMQ6b0sjdT2AXt9nalRuU9cBQXW8spmSpfZZL1K73EdsOvHM+J2d40kjWVtNMganNiwlKilICUFVUjq7Hnlksa87uKhgvtO9MLTZQCAn947iJlNaDRQgy2fnxB/nv30MMl7z6q2yghDoOW9XlVVheBgjg70taoqSy4Ag6FpK08Q+VK7C9AdlZaWAgBiYizZxHfu3Amj0YhJkyaJx3Tv3h0dO3bEli1bZAP02tpa1NbaPgTLyiwfGEajEUajcsk9BI3lA76+ztzscuzfkIPN/z0p2aY1CH5/fsGRegBmGFto2H5Lsb5OSv4+EOtBTVgXPqIFrvhTH3z9+j4AQFlRJUIjvZ+Lbn39C85YPguDww1I6hIOs8nWw60P0nhVTxodcPNzQ7Fp1XGc3msZwv6Pe9Zj4PSOYu9+ep8YnNx1ESUFlS6vabQb9VVUUIHQmMZ9YS48U469P53H8Gsyxd74qnJLg4AhSNf83zktcPkDvS1lNRphslt+zFhnhEbnfU+cffBp/56oN1pfA+fPWWuPcn29yWmfyWzC+Fu6Yf0HRxrKU++z95ip3tRQZkuZTCbbqAv7pees5QuLDpT0bJvMjuWVf510Bi1MDQ0UZrNZ0ohRXy+f9C7nSLHPnqdSf5tCIgMAjfz3m6L8CsSlhSE8PBwFBQUwm80ICQlpE0P83REEAXV1daiurm6x5yoIAqqqqnDhwgVERETAbDY7rb/OzynyN43Qjsd0mM1mXHnllSgpKcEvv/wCAPjoo49w2223SQJuABgyZAjGjx+PF154wek6Tz31FJ5++mmn7R999BFCQprf8t9UpjoN8tZahnl3mFbu9RInjuqrNMjf4DxcPHlCBXSB7fbXh4hINc5/b0m6Gdm9BmGdjKgr0cIQZobWy9i2YHMIjKU6GMJNSBxl6Umqr9RAEABDWOP+zhsrNSjYKD/FKKZvNYr2BkMfZkLS6Cqn/YIA5PwQBvtu4qSxFdCHeF+GnJ/CIBgt5yeOroQhzIyi3wNRdT4AEV1rEdHZt0uPCmYg50fL658yqdzr1xwAzv8YBpgtZU2dXi5uLz0WgPLjgQjtWIfontLvI4Vbg1FXrEdMv2qEJFsCVmv9Wz+Xy04EoOxoIEI6GBHTp6Y5T09UcjgQFacCEJZRh6jutagt1uHCVufvOAHR9agr1kMXbIap2tYbHNmtBuGZtkDH+jvnyBBhQmiqESUHgxCcZER9pRbGcstx1t8fOc35nqOkvPWhMNVoETeoCkHxlkaQujItCn+1TTOx3xceHo7w8HBxXXBqPrPZjPLycpSXl8vur6qqwuzZs1FaWoqIiAg/l47ao3bdgz5//nzs379fDM6batGiRViwYIH4uKysDGlpaZgyZYqib+Sqihr839rtAIApk6Y2eTmeglNl+N+GvU7bp0yd5DS8kuQZjUasWbMGkydP5vApBbEe1IN14Vtvf78JAFB6OAj1+RGoLLEEofPeGOW258loNGL16jVioBQcGIoZM8Y1qyz1dSas2LhZdt+YScPw5d69CAoIweihw7B37Xn0GpOCqCRLoFdTYcQHP2yVnnQuBTMe7Ovxvtbh/dbXAgAq9sfgpmeGYHXOQZw+fwl9+vdE9qjkpj85GaZ6M9798VcAwOTJUyRz3F2pqTRix7dnALOt53ny5Mnie2K3KQd7jp9DRkYnjJghzTD/9bG9yCsuw4AB/ZHZkPHb+pwnTZqI4PAA7FuXg61HTyI5KQUTZ0jXbm+qzdUnsP9ULrp0ycSQGRnIP1mKr7buczouJjoG+cVlCAkNQXm1rXGgR3YP9JmQKj7+fP9uXCytcDr/mj8PxflDxfj14AkkJiah7EINisotIzD69euHdXuPOJ0DAMP6jkFsavNzz/j7b9PHv21HeU0NRo4ZjsQM23fGjcIxHN5sSZiYmZyNflNsiQxNJhPq6+vb/Lzp+vp6bN68GSNGjIBe3zIhi0ajgV6vh07n+juydWQskb+02wD9vvvuwzfffIONGzciNdX2gZGUlIS6ujqUlJQgKipK3F5QUICkpCTZawUGBiIw0HlIocFgUPSLZ3CIfQIZTZPLYnYxsicgMAAGQ7v9FWoSpX8nyIL1oB6sC9/QG7Tiih3W4BwAqorrPc/hdhhR29z6MBgMSO4cibwTpZLtnQfEIyjE8llZUVSLj5+yNCBfOl+JPzwyCACQl+v8Rbgor8plmTZ/dhyGIB2SO0fif6/tQZbDUnCVxbUwGAw4vc8y5D4kPNDnv29are0F1Ov1Xl1/45fHcHirNFu99TyDwQBNwwgCnUHndD1NQ7Y1nc5yL/sgzfp+Cghs+Gw2+25Ore2+ljLp9fLXtTYIaR0ahhxfG/uGo6jEEHTqHYvsUSmITgpF/rGyhmtIe4m1WtdBlLkZ33Pk+Ptvk+PrM/iyDDFAv3S+UrKvvfzNNBqNqK+vR1hYmKLPub283qQe7W58jCAIuO+++/DFF19g3bp1yMjIkOwfOHAgDAYD1q5dK247cuQIzp49i+HDh/u7uM1in7X2wln5YTveuJQjnxnY28RBRETUsqbf01t2e3W55+HcZpPtb7n9cmLNEZ3k3CiQ3DlKdiRXwSlbUJ5/yvueqsrSWuxecxa/fX1KTGB6bLvzkqjvLrT1qAeH+f6LdlM+CS+cd+45tlfdsDa2zs3nrBiY23eiNhxuTTRmPz++uaxzy63fLTwOJ3fc7/DYvmEhNCoAI/+QhWjr6gENFxcEoU2ufS7L4fWJiA3GFfdbRo5czHH/+0JEbUu76/6cP38+PvroI/zvf/9DeHg48vMtrZORkZEIDg5GZGQk5s6diwULFiAmJgYRERG4//77MXz48FaVwV2kEQBB06y1UDd/flx2u9zyL0RE5H8hEfKJ4X759BhmLRrs9lxzrd1870zP2bi9oQtwDsQDgvViNnc5+SdLsf2bU847XERo9iuU5J90HdjXVNqGgSVnRbk8rsnsI1UfBZPWpGvGOucA2zEwlsbnDT3vDYF9SwbonjhOrXAX0Ls71n6ZNbfLxrbWQN5Nua1D9ksvVMNsMvN7F1E70e4C9LfeegsAMG7cOMn2lStXYs6cOQCAV199FVqtFtdeey1qa2sxdepU/OMf//BzSX0jKM6Emgt6n62Fbq81JmMhImqLdHr5P8iFZzyPnrqw3fdLNhkCnAOJwBC901rpVsvuXufyWq56UJvS8NyY9c691aSPQi+7hcsvNTLBW0NhtHprD7rvolZroOwpN5n1qTl+R3AMwgW7tgNXI/IEQVrPRXnyI/oajsaen87iwKZcXPXn/mIG/9ZCI/ObJI44ESzr0LsZ4U9EbUi7C9C9SagRFBSEZcuWYdmyZX4oUcvSaC3PtzlroXfoGoWcoyXO12aETkSkCjq9fNTUZWCCx3PNtb4PWnUG50giMEQvWTPcW8ZaE2oqjQgKNThtdyW5SyTyjpe63N9SBB9049p/T5Ed4m63RnjDCU6H6PS+H+Ju7cgWe9A9DnF334Nu/zwde+Xtv19Y14S3nOP6doIA/Ppfy4i/PWvOYtR1WR4KqA7i74zM62nfcGE2t9YhAkTUWBwr09Y11HBzWtHlgnMiIlIPxwCnc0N2b7NJmS/1cj3lgSF6aLQaDL+6s8wZUum9YyWPv37TeSURo5uRYWFRzr2nsR1CZY70AfuX3gcvt30QqpHtWW6Yn91wM7lbtkSALg5x97Jx3ukwN+c5XVNshBCkv8NuXt/yIttoA+sc/FbBzXOyf18r9V4mIv9rRX/BqCmsCVALz5Th5w8Po7Kk1v0JDuQS7hARkbo49qB3HmDpObeffy2nOQlE3THWOAfP1mU54zuGezw/Ik467L7wtPMcc7l7WPUYlYLw2CDJtkEzMlwc3Tz2waW3Cc3cHifpQffia5p9QG/t3G44TfBdfO6cJM5FF7r13p7moNvPLXcVuwuQBqaCm17kte8dEn8OaoFkgC1N7jWwz4Tv7rkTUdvCAL2Nsw5xP/pbAQ5sysWalQe9PjfvRClWv3ugpYpGREQ+EhIRIK6/Peq6LASHWwIUT1ncj/1WKP4cFuPDObsywUZgsKV8yZ0jPeYw6T+5o9M2xwDFXW6VoFADZjhktnc1T19tzB7mZouvneDwr91OjV0WdF+xzUF3P8RdbBTwMAfdnuPztB4qmKUBurfDvFtLXXui0WrE14JD3InaDwbobZzDEqKyvRByKktr8fmLO1ugRERE1BJuf3EU/vDXQegzLhX6huRS9W7yj9QbTbhw1rZ80+jruvqsLHJzza0Jr/QBOty2dJTLc697dDBCIgOctlc5NDbUVddLHqdlx4g/63RaxKWGY9Qs2zzk9F7SYfNKcttAYT832+0yaw3/2kXo1qOtvdy+7HW1NhzYeshdlUuQ3+80B91ul1NmeFsrhMluDrr9fHT32kaADth+BzjEnaj9YIDe1mmlf9C9bYH11OuSNTixyUUiIiLf0+q0SOwUAY1W49Uc5H/ev0GyBrnWy+WzvCuL7VopWVG49uGBkiDMcUh+cIQtIA+LDoROp0WPkcno2NMWVDtOubKfcwxYes3TsmMQ2yEMUYmWIfIBwbaGghZdosra4+tlj7WnZGdWOpk6cRvcW7O4W4e4NyOmM5sFSYBfnF/ZcH/3vyeu5qq7O8/lcHgBkhEC3n6HaU05bD3VkbYFGluISN3aXRb39saxB13wsgXW3by1KXf09CozMBERKcM6d7mqtA511fUICNajKLcSucdLkD0qRbZ/0V1vbWPZB+gdukY5ra9u7eG3Cg4zoLrM0jBsnT884eYeAGxLsB38NQ/9JnWEIAjQaDSoqZDOr9cZtLji/r6W59IQoWUNTsSx7QWI6RDmq6cmSwPfLcNtH4h51aggd2NN04I6a7Z8s8mMj57ahoBgPWYtGoTqciOKchsCdA8NOS6XWXP8PmL/PB2vaW3wcLh2W+5FdtWAYfkdMPs04R8RqRsD9DbO8QPR29Znx+y4oVGBqCypRXKXSGQNYu85EZGa6Qy2L/vHdhSg5+gO+PiZbeK2rEHOjaw+7UG3u5ZcQKfVajD35dE4/ftF5B0vxaAZnfB/j29BfHq4y0DFWFuPM/svYe0Hh5DZLx4Hf82T7A8I1DmdqzfocOWf+vvgGXmg0VgiU59kcfcwxF2coO06i7vY69qILvR9689h06pjGHVdFtJ7xqL0QrXlGmYBJQW29cet13ZVT+L3DA896JLn6Wq+ukP5ve1k2Lf+PHqPS/XqWMV5qCPrVI4d35/G5Nt6+qNERKQwBuhtnEbbtG8L9Q7ry864pzd+35CD4TM9L49DRETKMgTaPt5LCqsl+37/+Tw2rTrqdI4vA/SQSFvCOVeBUlCoAd2HJaP7sGQAwO0vj4ZBZnk28ZrhAfjm/1mWWzuwMUeyLyBIh/5TnBPL+YtPe9DtLiSbJM56HBx/sAW2YpK4RnS6blp1DADwy3+OIf0Z29QCwSxNXOdu/XnL8S7moLvjcj47kJgRIU7F8LaToaSgqhE3V7fkzpHIO1EqjjAhoraPc9DbOMcedEd1NfVY/e4BnNhdKNleX2f7NJ44pwcS0iMw8ZYeCIlwTtxDRETqYv+3es+as5J9RbmVskOFfTnEPaNPHHqPT8WkOT0QFOrdkleBwXq3Q7oriuWXCZ18ezZuf3E0wqKDZPf7ky+Sprsd+i17T/sIveEfrcy+RrAPrs1mQdJzfXhrnswZ9uWxXsOhB91piLuLGzo8tM9X0Jgh7rt+POP1sUry9Iwy+8cDAAK9fB8RUevHAL2tk6lh+w/sQ7/m4dj2Avzwz/2S+U3WIe7RSSFi7wYREbVOhWc8r+Dhyx50jVaDMdd3RTcffn5UuehB1AfooDMo/HVGfOnkw61Dm3Nx8Ndcry7lPrs5ZLrQnXdpmjgHXbyOw/rb9tPexAZ8l8usyfegOw1xdxea2o1wt38OjVlqbMsXJ1DrkOlf1Vy8ntYGCl+uaU9E6sYh7m2cRuf8YVZTaURwmKV3xf7D7sLZcjGRj3V92ciEED+UkoiIWpKr3md7Plwy268Mgc5Luvmd/JRpAJaGhXUfHAYAmIxmj3Oj7RvRtbLtDtb55a7LYe2tbnLib4cedGONLdD1lCRWDKg9tfc4d/zbPba9oPbfU84fLvZwURdlUTMXSfWsbNPxW8FzISKfYA96Gyc3xN1YY2sJ1+ltnwi1VbYPYGsLubv5gEREpF7hsXZDvr35bq/SAGD41e5zn6ghQNe4iUbt12vf+Inz3H9H9j2lnjKmW06QKY+LJGvecuxBtw+SB0xLbzjGRXHEddClBzgurScZKeBiGXTHHvTGBtxtIahtiTXtiUjdGH21cXIBepXdGud1dsG63BB3x6VwiIiodbjygX7iz605UMka7H7lEDUE6O56ix2Xxzq+s9DFkRbS7Obu1kF3zuJubSiwBnWNGRLuqgyW9dAtP3fqEwdDgK7hGBfnWkfAOxRd79jg7zZClz+ssf7via24lFvR9Av4gV1ty+5ngE7U/jBAb+tksrjv+O60+HPusRLxZ+sap4BtiLs+QAVffIiIqNGCw21JpX54e7/H49X69d+x59WRKgL0BnINIY4B+o//2i/5vHW6hiR5mswBjp3jcknirMc0dd6y3SVNRrMY6NvH0a4CRsGWJU6yXW9w3YPu+DzFOfSC0KzGpbrqevy08mCTz1eDZg6GIKJWiAF6GyfXgx4ZFyz+fO5gkfjztq9OikPxdn5vyX5aUVTTsgUkIqIWERDUNtLMOAZ2jgxBygfo7nLE1RsbGSXbRWJydegYs8sFupomrIPu6prHdxWKwbh9IkFXGdVdJYnTGaT15CY+l71eU108p+4edAjyr5cVe9CJ2h8G6G2cViZJnLshb5v+I50fd2rvRZ+XiYiIWp6r+cuhkS6Wy1Tp93+5DO19J6aJPxvUMNKrEUPcPbEPjrsPT2pSMbTNDups54VFBdp60HX2Abr88xJzxGnd96DDzVB+sXNBkK7B3h5ZX5umTlcgotaHAXobpwuWCdAbviwYa01O+w5vyZe0uPca26HlCkdERH7VMTsG1/xloOy+2A6hfi6Nd7QO67NnDU5Et6G2wFUVU7HczKE2NbIH3RpUB4booTfIPDdxzDOk/9rva+awaPuh8ZY56M6J30xGDz3oDtsdG1rcDnG3y1Tf3nuObWvaK1sOIvKftjH+jVzS6mXmwzUMS9vx/WnZc8xmAXFpYbh4rgIZfeJasnhERORHWr1Wtmd90u3dERhikDlDeU7ZwA1axHcMx6TbshGZEOziLGXIBVGNDtBdZEF3dZw9X/WgS5LE1duSxNkPcXc1MsDbOejullmzNTA0bw464GUmfBVr7pr2RNT6MEBv4zQyje/WHvTiPPkkNSajWfxC4Sk5DxERtR46vdY5mzYsvbWtha6hR92+F11pdouSOe1r6hB3uRwyssfL3NOWZK1Rt5ZlNgswN4wzty+Tq7n/LrO4O85B9zaLezMDUzUlEZQjvgwuXgJtM/MJEFHrw+irHbH2NBzbUdjwOAQAEJMiHdZorhfELxRyc/+IiKh1yj1WjOCwAIyalYUxN3RFSlYktIFmJGZEKF00r2l1KvxccjOkvLFJ4sSA1EXQ6rTZXZK4pvag2w9xN5ltQbddb3RKVhT6TkzDqOuyHM61ll96TbffJ5yyuDdcS2hGJnpredQe2HooHpPEEbU/KvyUo5YSnRgieVxXZQQAZA1KkGw31Zthqrd8ELAHnYio9Ro7u5vkcXW55e9+34lp6D0uFZfd3xvJ4yrVMY/bS2oMuNxlcW/8EHfLv55GZluPk3ZEW9dBtzuuCYGdZIi7yTbM3H6Iu0ajwahZWcgelSI919s56Hblcmp0aNhQX2dCeSNXk0nuEikdTq++XxdZGlfroHOZNaJ2h9FXOzDzoX4Yd1M3jL6+q7hNMAuobVhSLSDYgD4TUsV9lgDd8oXCMTkPERG1HilZUW73azQar4dSq0Vjh4z7hZsh2vV1TZyD3oy50/bz15vboGEZ4u66TFqH524bsi3drnU4V9rx75DFveFfd+vFu6LVaREQbJuyocYGHXtyUxTssQedqP1pZR/L1BQJ6eHoOboDgsNtS+vU15thrLFkcQ8I1mH4zM7iPlO9WZynzh50IqLWqy3+DW9sj7TSDvyS06jjxSHdrtbFdgqInYeU2wfS3gwRdwxi7R8LZgGCyXWA7tjA420g6S6Lu9uF0T0wm8zScraWuNbDOuhcZo2o/Wh7n9zkkv2Qr5O7ClFnDdAD9dAH6BAcYQngTfVmcR/noBMRtV6OmbNDIlysgd6K1DWM/lIT67Klcr21McmNW75Obji5hEY8UHYzIO289tSDbDYL2PSfY9IySOag2w1xlxkp4Bi0m8Us9LZtg2Z0cr6xZGy+2yI2SkVxraTRoN5oVncvuvuUAxziTtQOMfpqR+w/RH9675D4hcKa4VSnt+y/eL4CgGV4e3CYOpfdISIizxx70Fv7klMAUFVWp3QRnFh7jc8dKnLa19gM+a6GiLs8ToZ9PXvqed237hx+X3/e5f4d356GWUwSJ3Mvx3J6GUjKzZ0XHzfj97T8Uo1T48bxhuS4rRGHuBO1PwzQ27Gyi9UAbEul6Boy4148awnQ41LDWlXiICIiktLqpYFKQnq4QiXxna5D1LO8mqMTuy44bWtsFnIxyZqHDnTbCdYdtj16u4YZT3Pgj+90Dl4lSeLMglimxuSlacw67j4c4W69ueRhwemy5l5RMVqug07U7jBAb8esPejWZCrW4ezVlZbeiaCw1j8UkoioPXMc4j7+j90VKonv9B7XQekiuCTX89vY4dViMOxpiLt4fefNGq1GbHz3NCVALuh2bFQQk8R5CLrdldP5Jm6ObWaEXt7QAeGr6ynJOmqBQ9yJ2g8G6CSuj279kK6vtSaIa8WfaEREBK1Oi74T0gAANz0zTJIstDWK7RCmznXQG8jFr40NrMwma2+1q+epcbiu/Lrj1ulrxjqT2/vJNQQ4ZhYX3GRxd8VjfG53i9CoQIdzm/f9w/E1V/O3GbGBxeW69+xBJ2pv1PspRy1i4q09nLZZh7ZbvwxYP8y5xBoRUes36roszF8+AVEJIUoXpcmm390bsalhmDw3W+miuOWLHnRzI4eT2+asS7dbz7cG/K7I3sfhlP0bLJnoqyuMXpXJqTxyT8Xudek1xmFURDO+ftz89+HORWlMz7/KiHPQ2YVO1G4wQG9nug1LcpmwxtpjXl9rDdD560FERMrL7BePGx4fgtiUMKWL4pZsD3oj56DbetBd9aiKV5Zud4hqrY3vZpl142vthr3Lfda76q09sLExS8ZpZH6yu4fdLZySGbqJpy+/v6/LfR17xiAiNthdUdTHQ+DNZdaI2h9GYO2MRqNBcudI8bF1eDtg+zJg7UHXsQediIjIa43tQU/vFQtAmszPbLIE1C570L1cdkvbEPQ69qDvW38e7zy4EUe357u8j6tL9xrr/fz/5nRaa/Wuv566W7au6+BEn5dFaWLZGZ8TtRsM0Nsh+8zsGX3jxZ+tre3W5HHuPiCJiIhISm4otbu5w0nWBnO7Q7we4t5wjiCXJc7ufJNJ2oO+adVRAMCadw9aTpObV+4i+g+LDpTdLqsZQbFjj7q3ug51keFfxRG6+Eq7ao9hDzpRu8MIrB3SB9oCdEOA7VfAliSOPehERESNJde763a9cseM7Gag7EINANdZ3F0ts+a43foZbq53H9jJLcPmali+pyXb7EnK08gA2V2SWneXcplozc29KktrnRox1ETLddCJ2h0G6O2Qwa4H3b433dpjbqyzDq/jrwcREZEn1iXN4jvKrDPvJkJ3DCgv7Q7Cjm/PAHDzGezlAunW8z0liauXyfLu6oxGJY+1K2djO7Adn/vwqzvbX9jj+d2GOfSkuzjl4vkKvPfIr/jv8zsaV0BfErO4u9jv5ZQGImo7GIG1Q4ZAW7VLAnSHHnQtl1kjIiLyKD7NEpjLzTc3u+mcFQP0htNqCg3iPk/BsPVetizuDkni9PJD3B0lZUa4vLajxgTozRlV7tiDrjPYvrd4c93IeGmiOFc960e2WebhXzxX0cgS2lQU1+DwljyYZJLxNY58GdmDTtT+yKfz9oOzZ8/izJkzqKqqQnx8PHr27InAwEbMbaImsw/K7YN16wdYY5d4ISIiIhfc9aC76SZxNcTd5bUcz3eRJM6R/XcCkYtYs1Ej63w4B11vcH3f2NQwXDrvIcD2btBBk3z8zG+oq65HeVENBl+W0ejzPYXdXAedqP3xa4B++vRpvPXWW/jkk09w/vx5SQttQEAARo8ejTvvvBPXXnsttFp27rcUQ6D8EPdj2wskxzU1SQsREVF74q5X15v1q+WO8LzMmruz7ZLEuejZteajkQv8BB+kDD9/qLjJ5zo2BEheC4fn76kBQuYUL3Z4r65hybqzBy41KUAXi+IySZzlX8bnRO2H3yKwBx54AH379sWpU6fw7LPP4uDBgygtLUVdXR3y8/Px3XffYdSoUXjyySfRp08fbN++3V9Fa3f0LuagO2KATkRE5AU3S2G5TxLnOkJ0PQddel3bEHfpYToPc9CtSWLlyueqzMldIuV3yKgqq/P6WEeOQ9xlM803kFvn3bH8rs5XRXJ3T+ugWwvJCJ2o3fBbD3poaChOnjyJ2NhYp30JCQmYMGECJkyYgMWLF+OHH37AuXPnMHjwYH8Vr13RezmXy76nnYiIiFxpGIYsE6G760F3N8Rd4+00MxdZ3K15ZFz2oDc00MuVz9Vw6qQM7wN0e41OEufQQaCRJJyTXkyuAcKbUQsNV2tcwdzIP1nWtBM9FFVcZo1Z4ojaDb8F6EuWLPH62GnTprVgScj+86i20ij+POWOnlj9zgHbYWpoWSYiImrFXC1ZBtgniXMOvlwOcRe70J0uJnloHQXnKkC3NsL7J+5r7DJrDkPc3fSgyyXBk+tV90GxFGGbg65wQYjIbziGuR2y/6CLtluzNbVbtOS4eiM/DYiIiDzRuB3i7m6ZNYfHertjXQ1pFuNzaxZ3QfZa1iHmp/ZcsJXF7priaDq5HvRGBu16H4+4s/+eEhYd6BRID5iWDgAYNStLvgfd4etLU9ZH9xex9B7moHs/KoCIWju/B+jfffcd7rjjDjz88MM4fPiwZF9xcTEmTJjg7yK1O/ZzsRLSbcur6ByypLK1loiIyHuy87ndfZZae0cbHuqCbAcf/DXPw83c7z53sAiAdOi10W7Nc2sPuuwycA1PJCFdZl13Gbf8fThG/qGLZJvj46YSBGnArtEAw67KxK1LRqDvxDTZ3vK+k9Kk32lcBr9qCNHdE5+7AFSW1qLGbuQjEbVNfg3QP/roI1x55ZXIz8/Hli1b0L9/f3z44Yfi/rq6OmzYsMGfRWqXXA2bc1zGJCyGy94RERF50tQs7k496F7Ei83JSG6ssQXo1u8CsnPQBesx3n1NDA4LQEpWlGRbpz5xXp3rSXV5nVMPuEajQVh0kOVnmSA7NDIQtz43wif3b4wm9XJbcwi46uW3e37vPfIr3l24qSlFI6JWxK8B+osvvohXXnkF33zzDTZt2oT3338fd911F959911/FqPdc5Xkxf6DOK1HNLoMSPBXkYiIiFq/Rg4XdwrKvOnQdTjG1fWHXGFZ8su+F7yupl78Wew5l+lBdzVs3h37KXOdB8QjMj7YZZkbw2wSpOc7XGvGPX0QEhGAqfN6SbZ7FSv7uAPd1Xz/5mA+IKL2x6/roB87dgxXXHGF+Pi6665DfHw8rrzyShiNRlx99dX+LE67FRoViD/+bTgCg52rv/e4VJRdqsZl9/RpFUO/iIiIFOcminKVEd1ynvUg512GIPfzusXeWjGLu7QMoVGWUXDBEQHitvyTpfYXkF5HUuaGa2o1iE0Nw6XzFQgON7gtjyFAh3vfGg8Izr3azQ0y7a/n+DxTsqIw54WRbpesczUdwNffcuprzdAbGjcf39Oa83LfxQRBcP98iahV82uAHhERgYKCAmRkZIjbxo8fj2+++QaXX345zp8/78/itGuSlm07Y27o6ueSEBERtW5u4mwPQ9wdu8NtPxoCXAR6DucILtZZs2X/tl103Qe23D9mszVAd76FtSdYp9fg+kcH49zhIsR39DwfXaPRtEjmNY2bHnTxvg68Gm7u4yDXWGdCENw3ZDS2KLKdJQLUkeGOiFqEX4e4DxkyBN9//73T9rFjx+Lrr7/Ga6+95s/iEBERETWf2yzubk5zE2TNuLeP21vZbiB/La3rJO2W7Wbrv84HWAN0rV4LjVaDjtmxCA4LcDrOX5rSW2yfb8fVevO+7oQ21po8H+TI0zroLuJzImq7/BqgP/jggwgKCpLdN27cOHz99de45ZZb/FkkIiIiIp+QDYYbkSTOeuhVf+6HxE4Rzid4d1nLtbXOPej288QFN0PcTQ3LrOq8TBLX0poSSNs3KAQE+WfAaH1dEwJ0keckcSIuuUbUpvn1L+/YsWOxaNEil/vHjx+PlStX+rFERERERM3lOoKUXcZMPE1+iLvbHmNXu2QynQPSADy2g12A7maIe31dQw+6i1Vf/MGa9G309V0lr0djgvUuAy3Jbl0Nd28NPeha2eH7jb8NEbUefp2DTkRERNTW2GIoz2PcOw9IwIldhZbztPaH2Y5zNSQbsIvPrTniXCVAE3vQbduO7yh0KpZsD3q9JdDU6n0TwTZliHqXgQno2HMMAoL0OHe4yP5ijbix5R/XAa2P56A3JUBv4PJpyW1ngE7UpikydolzzYmIiKjNcBMI2m/r2DNGMnTdOUmcxrrDzb0cJ7wL9kVwOsxVFnlrkriL5yqc9tWrZIi7dWh6UzOWi+e5Cmh93INuHXnQGJ5ibY1GIzPQghE6UVvm97+8jz76KN566y1/35aIiIjI76wBcnKXSEyak+02KLSGXd4EpIJjD7pjFnet8xB3x3LlnyzFhbPlTvusAbpWr7456E2Jqf03xL3e80GuuGuTcZiHziHuRG2b34a4C4KAu+66C6tXr8amTZv8dVsiIiKiFuU47HzbVydRUlCFKXN7wmSyBLsjrumC4PAAabBp/0Cwne9uiLtTICe/ypoY1NnPgQ8ON6C63Gg5TQA2f3Zc9ha2JHHqWMtL08QIXeMhk72v1dU0ZQ6658JZnr/dcQzQido0vwXof/jDH7B161Zs2LABaWlp/rotERERUcty6Ird8d1pAEBm/3hUFNU2HOIcWbpe/st1FOpyj1OSuIYf7ALA2mpbD6/ZZEbeiVLZS/m8B91NUB0RJ7+6j+T0JrYTyCXKk9vvK42dgy4Iglg9buvcoRq8WuOdiFotv41d+uKLL/D000+jS5cu/rolERERkd84Bk6r3zkg/mwNsqQZyW0/C+L/vAtIHWM0x3NsPei2A80m28+lhdWS40dfnyX+bGpYLsxXPegaNxG6zotGANmlxry6bwM/xbPGxvag25VL6+Y5Oj1/xudEbZrfAvQHH3wQCxcuxI4dO/x1SyIiIiK/akxvrdMmr5ZZc5yP7H5+tXW32Sy4Dey0Oq0YCIo96H4Y4q71JhGdq2kBHi/uvgfd10nijDWNm4MuKZebsjgG74zPido2vw1xf/nllxEbG4tp06bh559/Rq9evfx1ayIiIqIWYx8Mu8qaLg5Tto+1HINtx2PlriMe7D5Msy2zZjnObPKcYVyjsZTBVN/ySeKCIwJQXVaHjL5xHo/VG3S2B41fZc3Nft9G6HWNHeJuVyVuRwk4dqC7+B0jorbBr+ugP/roo4iLi8PUqVORk5Pjz1sTERERtTj7YeT2rD2/0iRxdgfYJ4nzJou79V8XWdy14vxr9+WyMhnNliDRJIjLhflsmTWZp3PdosE4f7gIWYMTPZ5uCLRfML4RtxWXmvO+XI2VkB6OwjOWTPiNHeJu34PursrdDX8norbHrwE6ANx5552IjY31922JiIiIWoQ1oN7w8RF0dRVwapx+cB2Iu4vHXGZxd0gSZ81gLvagu49sK0tqxV5cWw96ywWGYdGB6D482atjg8MDxJ8bFaw6rRnvYjcswXJjhs9v/PgICs+Wi68VAJzccwEAsHftOdRWGTHkiky315CMcHc3B915IXQiasP8HqADwLXXXqvEbYmIiIh8rs4691gAKkpqZY+xBpaSWEsSdwle9aA7Jj4TXKyzJmYw9zJAr62uF8sm9qD7aIh7c5OlBwTpcfXCAQAAncH7MjnOw3dHEBpXzt83yI8Erak04pdPjwEAug1LQmR8iOt72g1V17qrc8f4nFncido0vyWJIyIiImqL7AMmT0ttuRziDkCATBDv7gJuNmscEqS5m4OuD9Ci/+SOYiNCvdHyHPyRJM5bKVlRSMmKatQ55UU1AIDfvj4lu1+SRd9HQW91eZ34c121+98FyT3d5R1wTBLH+JyoTVOkBx0Atm/fjvXr16OwsBBms/RD45VXXlGoVDbLli3Diy++iPz8fPTt2xdvvvkmhgwZonSxiIiISGXs5zjX13mYh+ximTXLhVxsl7unIHahu7iNtQfd8thVD3pUYghueHwIdAateI7J6NsedKWcPVDk/cFmADqPR3lkHX0AACYPifkkQ9zdroOunoYSImp5igTozz33HB5//HF069YNiYmJLtcEVcqqVauwYMECLF++HEOHDsVrr72GqVOn4siRI0hISFC6eERERKQi9r3TddXyS23JBcjSOdCwBeju4mIvvyaJc9AF90Pce4xMFoeNW+ecW0cBqKkHvUU4zEH3BaNdA4253v017Ye4N2JlPQ5xJ2rjFAnQX3/9daxYsQJz5sxR4vYevfLKK5g3bx5uu+02AMDy5cvx7bffYsWKFfjrX/+qcOmIiIhITeyD3zoXmbwFsXfcts1Vp0Sj5qCLWdwdksQ5zEF31ZvbZYCt40HfEKhbGxl8lsVdZUwmM/KOlYgjBQDgu+W/I6VLJPpOTvV4vrtlzuxHUHzxyi70HtMBY27sJn8duwz8jepBZ3xO1KYpEqBrtVqMHDlSiVt7VFdXh507d2LRokXiNq1Wi0mTJmHLli0KloyIiIjUyGwXsP208qDsMdagTh9gG0dt3xN6Zn8RBFNDIOZFx3VJYRVO7b2A4vwq2VM04nxyM07tvYCyizWy19HaBeHWIe3WYvkqi7saRkee2X/J8oMG2P7NKRScKpPsP3ewCOcOFiEqORjVhTqc3V8End5SVxqNxhJEAyjOr0Le8RKX98k9ZrdPsCSTS+0RI3tsbZXRdn03HDPXnztchKBQg3gPp3jdRz3sSnfUm0wm1JW1zUYiIncUCdAffPBBLFu2DK+99poSt3fr4sWLMJlMSEyULpOSmJiIw4cPy55TW1uL2lpb1tayMssffaPRCKPR2HKF9cB6byXLQBasC3VgPagH60IdWA++ERoViIvnKtweI8BkeZ21tqintsb2uv/07iHxZ41OcF0nGsv5J3ZdwIldF2ybtdJ6FARLT25tVT2+e+t3cbvOoJX0HENrEs/TBzgEQxqzT3439IEaRX7HohKDUVJQDQD45v/t9eqcH/95EEAIfth5oEn33Pn9Gadt3y//XeZIG63O/evj2IO+9r1DLo5se0I7GhT/+6T0/an90QgKTGQxm8247LLLcPToUWRnZ8NgMEj2f/755/4ukig3NxcdOnTA5s2bMXz4cHH7ww8/jA0bNmDbtm1O5zz11FN4+umnnbZ/9NFHCAlxvbwGERERtX61RTpc2Ob68z4guh4JwyyBomACclaHAwA6TCtH2bEA1F5q6C8RgKD4ekRk1bm6FOrKtCg9HGjrbW8Q2rEOoR1s898FASg5EAhjuTTzWWiqEcZyLepKdE73qsrTo+KMARA00AWbEdO7BppmJE6rPK9HbZEe0b1q3M+rbyF1pVoUbg4FABgibEPPTbUamGu10OgEp9fRENlwnOO344bHjq+nIdIEfYgZgbEmVJ03AAJQVyo9JiDKfeLA4CQjwjNcB4FVOXpUnA1AXYnO6bnA1aAL5Qct+ERwohHhmcoGyFVVVZg9ezZKS0sRERGhaFmofVCkB/2BBx7A+vXrMX78eMTGxqpi6JNVXFwcdDodCgoKJNsLCgqQlJQke86iRYuwYMEC8XFZWRnS0tIwZcoURd/IRqMRa9asweTJk50aQci/WBfqwHpQD9aFOrAefOiPjTj2CudNLVIXl/nmMq3ajY07nO8J9VBLXVhHxhL5iyIB+vvvv4/PPvsMl12mvk+OgIAADBw4EGvXrsXMmTMBWHr8165di/vuu0/2nMDAQAQGBjptNxgMqvjjrpZyEOtCLVgP6sG6UAfWg3qwLtSB9aAeStcFfw/I3xQJ0GNiYtC5c2clbu2VBQsW4NZbb8WgQYMwZMgQvPbaa6isrBSzuhMRERERERH5miIB+lNPPYXFixdj5cqVqpyjff311+PChQt48sknkZ+fj379+uGHH35wShxHRERERERE5CuKBOhvvPEGTpw4gcTERHTq1Mlp6MiuXbuUKJbEfffd53JIOxEREREREZGvKRKgW+d2ExEREREREZGFIgH64sWLlbgtERERERERkWopsColsH37dtn1xLdt24YdO3YoUCIiIiIiIiIiZSkSoM+fPx/nzp1z2p6Tk4P58+crUCIiIiIiIiIiZSkSoB88eBADBgxw2t6/f38cPHhQgRIRERERERERKUuRAD0wMBAFBQVO2/Py8qDXKzItnoiIiIiIiEhRigToU6ZMwaJFi1BaWipuKykpwaOPPorJkycrUSQiIiIiIiIiRSnSXf3SSy9hzJgxSE9PR//+/QEAe/bsQWJiIv79738rUSQiIiIiIiIiRSkSoHfo0AH79u3Dhx9+iL179yI4OBi33XYbbrzxRhgMBiWKRERERERERKQoxSZ8h4aG4s4771Tq9kRERERERESqosgcdCIiIiIiIiKSYoBOREREREREpAIM0ImIiIiIiIhUwK8B+smTJ/15OyIiIiIiIqJWw68Bep8+fdCrVy88+uij2LZtmz9vTURERERERKRqfg3QL168iCVLlqCwsBBXXXUVkpOTMW/ePHz99deoqanxZ1GIiIiIiIiIVMWvAXpQUBCuuOIKvPPOO8jLy8Nnn32G2NhYPPLII4iLi8PMmTOxYsUKXLhwwZ/FIiIiIiIiIlKcYkniNBoNRowYgeeffx4HDx7E7t27MXr0aLz33ntITU3FsmXLlCoaERERERERkd/plS6AVVZWFhYuXIiFCxfi0qVLKCoqUrpIRERERERERH6jmgDdXmxsLGJjY5UuBhEREREREZHfcB10IiIiIiIiIhVggE5ERERERESkAqoL0E0mk9JFICIiIiIiIvI71QToR48excMPP4zU1FSli0JERERERETkd4oG6FVVVVi5ciVGjx6N7OxsbNy4EQsWLFCySERERERERESKUCSL+9atW/HOO+/g008/RceOHXHo0CGsX78eo0ePVqI4RERERERERIrzaw/6yy+/jJ49e+IPf/gDoqOjsXHjRvz+++/QaDRcVo2IiIiIiIjaNb/2oD/yyCN45JFH8Mwzz0Cn0/nz1kRERERERESq5tce9L/97W/49NNPkZGRgUceeQT79+/35+2JiIiIiIiIVMuvAfqiRYtw9OhR/Pvf/0Z+fj6GDh2Kvn37QhAEFBcX+7MoRERERERERKqiSBb3sWPH4v3330d+fj7uvfdeDBw4EGPHjsWIESPwyiuvKFEkIiIiIiIiIkUpusxaeHg47rrrLmzbtg27d+/GkCFD8PzzzytZJCIiIiIiIiJFKBqg2+vduzdee+015OTkKF0UIiIiIiIiIr9TZB10ANi+fTvWr1+PwsJCmM1mcbtGo8HLL7+sVLGIiIiIiIiIFKFIgP7cc8/h8ccfR7du3ZCYmAiNRiPus/+ZiIiIiIiIqL1QJEB//fXXsWLFCsyZM0eJ2xMRERERERGpjiJz0LVaLUaOHKnErYmIiIiIiIhUSZEA/cEHH8SyZcuUuDURERERERGRKikyxP2hhx7CZZddhs6dOyM7OxsGg0Gy//PPP1eiWERERERERESKUSRAf+CBB7B+/XqMHz8esbGxTAxHRERERERE7Z4iAfr777+Pzz77DJdddpkStyciIiIiIiJSHUXmoMfExKBz585K3JqIiIiIiIhIlRQJ0J966iksXrwYVVVVStyeiIiIiIiISHUUGeL+xhtv4MSJE0hMTESnTp2cksTt2rVLiWIRERERERERKUaRAH3mzJlK3JaIiIiIiIhItRQJ0BcvXqzEbYmIiIiIiIhUy29z0AVB8NetiIiIiIiIiFodvwXoPXv2xCeffIK6ujq3xx07dgz33HMPnn/+eT+VjIiIiIiIiEh5fhvi/uabb+KRRx7Bvffei8mTJ2PQoEFISUlBUFAQiouLcfDgQfzyyy84cOAA7rvvPtxzzz3+KhoRERERERGR4vwWoE+cOBE7duzAL7/8glWrVuHDDz/EmTNnUF1djbi4OPTv3x+33HILbrrpJkRHR/urWERERERERESq4PckcaNGjcKoUaP8fVsiIiIiIiIiVfPbHHQiIiIiIiIico0BOhEREREREZEKMEAnIiIiIiIiUgEG6EREREREREQqwACdiIiIiIiISAUUCdDHjh2LDz74ANXV1UrcnoiIiIiIiEh1FAnQ+/fvj4ceeghJSUmYN28etm7dqkQxiIiIiIiIiFRDkQD9tddeQ25uLlauXInCwkKMGTMG2dnZeOmll1BQUKBEkYiIiIiIiIgUpdgcdL1ej2uuuQb/+9//cP78ecyePRtPPPEE0tLSMHPmTKxbt06pohERERERERH5neJJ4n777TcsXrwYL7/8MhISErBo0SLExcXh8ssvx0MPPaR08YiIiIiIiIj8Qq/ETQsLC/Hvf/8bK1euxLFjx3DFFVfg448/xtSpU6HRaAAAc+bMwbRp0/DSSy8pUUQiIiIiIiIiv1KkBz01NRXvvPMObr31Vpw/fx7//e9/MW3aNDE4B4A+ffpg8ODBPr3v6dOnMXfuXGRkZCA4OBidO3fG4sWLUVdXJzlu3759GD16NIKCgpCWloalS5f6tBxEREREREREjhTpQV+7di1Gjx7t9piIiAisX7/ep/c9fPgwzGYz/vnPf6JLly7Yv38/5s2bh8rKSrGnvqysDFOmTMGkSZOwfPly/P7777j99tsRFRWFO++806flISIiIiIiIrJSpAd98eLFKCkpcdpeVlaGCRMmtNh9p02bhpUrV2LKlCnIzMzElVdeiYceegiff/65eMyHH36Iuro6rFixAj179sQNN9yABx54AK+88kqLlYuIiIiIiIhIkR70DRs2OA0rB4Camhps2rTJr2UpLS1FTEyM+HjLli0YM2YMAgICxG1Tp07FCy+8gOLiYkRHRztdo7a2FrW1teLjsrIyAIDRaITRaGzB0rtnvbeSZSAL1oU6sB7Ug3WhDqwH9WBdqAPrQT3UUhdK35/aH78G6Pv27QMACIKAgwcPIj8/X9xnMpnwww8/oEOHDn4rz/Hjx/Hmm29KEtHl5+cjIyNDclxiYqK4Ty5AX7JkCZ5++mmn7atXr0ZISIiPS914a9asUboI1IB1oQ6sB/VgXagD60E9WBfqwHpQD6XroqqqStH7U/vj1wC9X79+0Gg00Gg0skPZg4OD8eabbzb6un/961/xwgsvuD3m0KFD6N69u/g4JycH06ZNw6xZszBv3rxG39PeokWLsGDBAvFxWVkZ0tLSMGXKFERERDTr2s1hNBqxZs0aTJ48GQaDQbFyEOtCLVgP6sG6UAfWg3qwLtSB9aAeaqkL68hYIn/xa4B+6tQpCIKAzMxM/Pbbb4iPjxf3BQQEICEhATqdrtHXXbhwIebMmeP2mMzMTPHn3NxcjB8/HiNGjMDbb78tOS4pKQkFBQWSbdbHSUlJstcODAxEYGCg03aDwaCKP+5qKQexLtSC9aAerAt1YD2oB+tCHVgP6qF0XfD3gPzNrwF6eno6AMBsNvv0uvHx8ZJg352cnByMHz8eAwcOxMqVK6HVSvPkDR8+HI899hiMRqP4hlyzZg26desmO7ydiIiIiIiIyBf8FqB/9dVXmD59OgwGA7766iu3x1555ZUtUoacnByMGzcO6enpeOmll3DhwgVxn7V3fPbs2Xj66acxd+5cPPLII9i/fz9ef/11vPrqqy1SJiIiIiIiIiLAjwH6zJkzkZ+fj4SEBMycOdPlcRqNBiaTqUXKsGbNGhw/fhzHjx9HamqqZJ8gCACAyMhIrF69GvPnz8fAgQMRFxeHJ598kmugExERERERUYvyW4BuP6zd10PcvTVnzhyPc9UBoE+fPn5f7o2IiIiIiIjaN63nQ/yjpKRE6SIQERERERERKUaRAP2FF17AqlWrxMezZs1CTEwMOnTogL179ypRJCIiIiIiIiJFKRKgL1++HGlpaQAs88J/+ukn/PDDD5g+fTr+8pe/KFEkIiIiIiIiIkX5dZk1q/z8fDFA/+abb3DddddhypQp6NSpE4YOHapEkYiIiIiIiIgUpUgPenR0NM6dOwcA+OGHHzBp0iQAlkzqLZXBnYiIiIiIiEjNFOlBv+aaazB79mxkZWXh0qVLmD59OgBg9+7d6NKlixJFIiIiIiIiIlKUIgH6q6++ik6dOuHcuXNYunQpwsLCAAB5eXm49957lSgSERERERERkaIUCdANBgMeeughp+0PPvigAqUhIiIiIiIiUp4iAToAHDt2DOvXr0dhYSHMZrNk35NPPqlQqYiIiIiIiIiUoUiA/q9//Qv33HMP4uLikJSUBI1GI+7TaDQM0ImIiIiIiKjdUSRAf/bZZ/H3v/8djzzyiBK3JyIiIiIiIlIdRZZZKy4uxqxZs5S4NREREREREZEqKRKgz5o1C6tXr1bi1kRERERERESqpMgQ9y5duuCJJ57A1q1b0bt3bxgMBsn+Bx54QIliERERERERESlGkQD97bffRlhYGDZs2IANGzZI9mk0GgboRERERERE1O4oEqCfOnVKidsSERERERERqZYic9Ct6urqcOTIEdTX1ytZDCIiIiIiIiLFKRKgV1VVYe7cuQgJCUHPnj1x9uxZAMD999+P559/XokiERERERERESlKkQB90aJF2Lt3L37++WcEBQWJ2ydNmoRVq1YpUSQiIiIiIiIiRSkyB/3LL7/EqlWrMGzYMGg0GnF7z549ceLECSWKRERERERERKQoRXrQL1y4gISEBKftlZWVkoCdiIiIiIiIqL1QJEAfNGgQvv32W/GxNSh/5513MHz4cCWKRERERERERKQoRYa4P/fcc5g+fToOHjyI+vp6vP766zh48CA2b97stC46ERERERERUXugSA/6qFGjsGfPHtTX16N3795YvXo1EhISsGXLFgwcOFCJIhEREREREREpSpEedADo3Lkz/vWvfyl1eyIiIiIiIiJVUSRALy0txZo1a3D69GloNBpkZmZi4sSJiIiIUKI4RERERERERIrze4D+f//3f7jvvvtQVlYm2R4ZGYnly5fj+uuv93eRiIiIiIiIiBTn1znou3btwm233YaZM2di9+7dqK6uRlVVFXbs2IErrrgCN998M/bu3evPIhERERERERGpgl970N98803MnDkT7733nmT7gAED8MEHH6Cqqgqvv/46VqxY4c9iERERERERESnOrz3ov/76K+666y6X+++++2788ssvfiwRERERERERkTr4NUDPzc1F165dXe7v2rUrcnJy/FgiIiIiIiIiInXwa4BeVVWFoKAgl/sDAwNRU1PjxxIRERERERERqYPfs7j/+OOPiIyMlN1XUlLi38IQERERERERqYTfA/Rbb73V7X6NRuOnkhARERERERGph18DdLPZ7M/bEREREREREbUafp2DTkRERERERETyGKATERERERERqQADdCIiIiIiIiIVYIBOREREREREpAIM0ImIiIiIiIhUQJEAPTMzE5cuXXLaXlJSgszMTAVKRERERERERKQsRQL006dPw2QyOW2vra1FTk6OAiUiIiIiIiIiUpZf10H/6quvxJ9//PFHREZGio9NJhPWrl2LTp06+bNIRERERERERKrg1wB95syZAACNRoNbb71Vss9gMKBTp054+eWX/VkkIiIiIiIiIlXwa4BuNpsBABkZGdi+fTvi4uL8eXsiIiIiIiIi1fJrgG516tQpJW5LREREREREpFqKBOgAsHbtWqxduxaFhYViz7rVihUrFCoVERERERERkTIUCdCffvppPPPMMxg0aBCSk5Oh0WiUKAYRERERERGRaigSoC9fvhzvvfcebr75ZiVuT0RERERERKQ6iqyDXldXhxEjRihxayIiIiIiIiJVUiRAv+OOO/DRRx8pcWsiIiIiIiIiVVJkiHtNTQ3efvtt/PTTT+jTpw8MBoNk/yuvvKJEsYiIiIiIiIgUo0iAvm/fPvTr1w8AsH//fsk+JowjIiIiIiKi9kiRAH39+vVK3JaIiIiIiIhItRSZg251/Phx/Pjjj6iurgYACIKgZHGIiIiIiIiIFKNIgH7p0iVMnDgRXbt2xYwZM5CXlwcAmDt3LhYuXKhEkYiIiIiIiIgUpUiA/uCDD8JgMODs2bMICQkRt19//fX44YcflCgSERERERERkaIUmYO+evVq/Pjjj0hNTZVsz8rKwpkzZ5QoEhEREREREZGiFOlBr6yslPScWxUVFSEwMFCBEhEREREREREpS5EAffTo0fjggw/ExxqNBmazGUuXLsX48eOVKBIRERERERGRohQZ4r506VJMnDgRO3bsQF1dHR5++GEcOHAARUVF+PXXX5UoEhEREREREZGiFOlB79WrF44ePYpRo0bhqquuQmVlJa655hrs3r0bnTt3VqJIRERERERERIrye4BuNBoxceJEFBYW4rHHHsN//vMffPfdd3j22WeRnJzst3LU1taiX79+0Gg02LNnj2Tfvn37MHr0aAQFBSEtLQ1Lly71W7mIiIiIiIioffJ7gG4wGLBv3z5/39bJww8/jJSUFKftZWVlmDJlCtLT07Fz5068+OKLeOqpp/D2228rUEoiIiIiIiJqLxQZ4v7HP/4R7777rhK3BgB8//33WL16NV566SWnfR9++CHq6uqwYsUK9OzZEzfccAMeeOABvPLKKwqUlIiIiIiIiNoLRZLE1dfXY8WKFfjpp58wcOBAhIaGSva3ZDBcUFCAefPm4csvv5Rd6m3Lli0YM2YMAgICxG1Tp07FCy+8gOLiYkRHRzudU1tbi9raWvFxWVkZAMtwfqPR2ALPwjvWeytZBrJgXagD60E9WBfqwHpQD9aFOrAe1EMtdaH0/an90QiCIPj7pu6WUtNoNFi3bl2L3FcQBMyYMQMjR47E448/jtOnTyMjIwO7d+9Gv379AABTpkxBRkYG/vnPf4rnHTx4ED179sTBgwfRo0cPp+s+9dRTePrpp522f/TRR7KNAEREREREpH5VVVWYPXs2SktLERERoXRxqB3wew+6yWTC008/jd69e8v2RjfFX//6V7zwwgtujzl06BBWr16N8vJyLFq0yCf3tVq0aBEWLFggPi4rK0NaWhqmTJmi6BvZaDRizZo1mDx5MgwGg2LlINaFWrAe1IN1oQ6sB/VgXagD60E91FIX1pGxRP7i9wBdp9NhypQpOHTokM8C9IULF2LOnDluj8nMzMS6deuwZcsWBAYGSvYNGjQIN910E95//30kJSWhoKBAst/6OCkpSfbagYGBTtcELAnx1PDHXS3lINaFWrAe1IN1oQ6sB/VgXagD60E9lK4L/h6QvykyB71Xr144efIkMjIyfHK9+Ph4xMfHezzujTfewLPPPis+zs3NxdSpU7Fq1SoMHToUADB8+HA89thjMBqN4htyzZo16Natm88aFIiIiIiIiIgcKZLF/dlnn8VDDz2Eb775Bnl5eSgrK5P811I6duyIXr16if917doVANC5c2ekpqYCAGbPno2AgADMnTsXBw4cwKpVq/D6669LhrATERERERER+ZoiPegzZswAAFx55ZXQaDTidkEQoNFoYDKZlCgWACAyMhKrV6/G/PnzMXDgQMTFxeHJJ5/EnXfeqViZiIiIiIiIqO1TJEBfv369Erd10qlTJ8glse/Tpw82bdqkQImIiIiIiIiovVIkQB87dqwStyUiIiIiIiJSLUUC9I0bN7rdP2bMGD+VhIiIiIiIiEgdFAnQx40b57TNfi66knPQiYiIiIiIiJSgSBb34uJiyX+FhYX44YcfMHjwYKxevVqJIhEREREREREpSpEe9MjISKdtkydPRkBAABYsWICdO3cqUCoiIiIiIiIi5SjSg+5KYmIijhw5onQxiIiIiIiIiPxOkR70ffv2SR4LgoC8vDw8//zz6NevnxJFIiIiIiIiIlKUIgF6v379oNFonNYgHzZsGFasWKFEkYiIiIiIiIgUpUiAfurUKcljrVaL+Ph4BAUFKVEcIiIiIiIiIsUpEqCnp6crcVsiIiIiIiIi1fJrkrh169YhOzsbZWVlTvtKS0vRs2dPbNq0yZ9FIiIiIiIiIlIFvwbor732GubNm4eIiAinfZGRkbjrrrvwyiuv+LNIRERERERERKrg1wB97969mDZtmsv9U6ZM4RroRERERERE1C75NUAvKCiAwWBwuV+v1+PChQt+LBERERERERGROvg1QO/QoQP279/vcv++ffuQnJzsxxIRERERERERqYNfA/QZM2bgiSeeQE1NjdO+6upqLF68GJdffrk/i0RERERERESkCn5dZu3xxx/H559/jq5du+K+++5Dt27dAACHDx/GsmXLYDKZ8Nhjj/mzSERERERERESq4NcAPTExEZs3b8Y999yDRYsWQRAEAIBGo8HUqVOxbNkyJCYm+rNIRERERERERKrg1wAdANLT0/Hdd9+huLgYx48fhyAIyMrKQnR0tL+LQkRERERERKQafg/QraKjozF48GClbk9ERERERESkKn5NEkdERERERERE8higExEREREREakAA3QiIiIiIiIiFWCATkRERERERKQCDNCJiIiIiIiIVIABOhEREREREZEKMEAnIiIiIiIiUgEG6EREREREREQqwACdiIiIiIiISAUYoBMRERERERGpAAN0IiIiIiIiIhVggE5ERERERESkAgzQiYiIiIiIiFSAAToRERERERGRCjBAJyIiIiIiIlIBBuhEREREREREKsAAnYiIiIiIiEgFGKATERERERERqQADdCIiIiIiIiIVYIBOREREREREpAIM0ImIiIiIiIhUgAE6ERERERERkQowQCciIiIiIiJSAQboRERERERERCrAAJ2IiIiIiIhIBRigExEREREREakAA3QiIiIiIiIiFWCATkRERERERKQCDNCJiIiIiIiIVIABOhEREREREZEKMEAnIiIiIiIiUgEG6EREREREREQqwACdiIiIiIiISAUYoBMRERERERGpAAN0IiIiIiIiIhVggE5ERERERESkAgzQiYiIiIiIiFSAAToRERERERGRCjBAJyIiIiIiIlIBBuhEREREREREKsAAnYiIiIiIiEgF2l2A/u2332Lo0KEIDg5GdHQ0Zs6cKdl/9uxZXHbZZQgJCUFCQgL+8pe/oL6+XpnCEhERERERUbuhV7oA/vTZZ59h3rx5eO655zBhwgTU19dj//794n6TyYTLLrsMSUlJ2Lx5M/Ly8nDLLbfAYDDgueeeU7DkRERERERE1Na1mwC9vr4ef/rTn/Diiy9i7ty54vbs7Gzx59WrV+PgwYP46aefkJiYiH79+uFvf/sbHnnkETz11FMICAhQouhERERERETUDrSbIe67du1CTk4OtFot+vfvj+TkZEyfPl3Sg75lyxb07t0biYmJ4rapU6eirKwMBw4cUKLYRERERERE1E60mx70kydPAgCeeuopvPLKK+jUqRNefvlljBs3DkePHkVMTAzy8/MlwTkA8XF+fr7La9fW1qK2tlZ8XFZWBgAwGo0wGo2+fipes95byTKQBetCHVgP6sG6UAfWg3qwLtSB9aAeaqkLpe9P7U+rD9D/+te/4oUXXnB7zKFDh2A2mwEAjz32GK699loAwMqVK5GamopPP/0Ud911V5PLsGTJEjz99NNO21evXo2QkJAmX9dX1qxZo3QRqAHrQh1YD+rBulAH1oN6sC7UgfWgHkrXRVVVlaL3p/an1QfoCxcuxJw5c9wek5mZiby8PADSOeeBgYHIzMzE2bNnAQBJSUn47bffJOcWFBSI+1xZtGgRFixYID4uKytDWloapkyZgoiIiEY9H18yGo1Ys2YNJk+eDIPBoFg5iHWhFqwH9WBdqAPrQT1YF+rAelAPtdSFdWQskb+0+gA9Pj4e8fHxHo8bOHAgAgMDceTIEYwaNQqA5Y1/+vRppKenAwCGDx+Ov//97ygsLERCQgIAS6tdRESEJLB3FBgYiMDAQKftBoNBFX/c1VIOYl2oBetBPVgX6sB6UA/WhTqwHtRD6brg7wH5W6sP0L0VERGBu+++G4sXL0ZaWhrS09Px4osvAgBmzZoFAJgyZQqys7Nx8803Y+nSpcjPz8fjjz+O+fPnywbgRERERERERL7SbgJ0AHjxxReh1+tx8803o7q6GkOHDsW6desQHR0NANDpdPjmm29wzz33YPjw4QgNDcWtt96KZ555RuGSExERERERUVvXrgJ0g8GAl156CS+99JLLY9LT0/Hdd9/5sVRERERERERE7WgddCIiIiIiIiI1Y4BOREREREREpAIM0ImIiIiIiIhUgAE6ERERERERkQowQCciIiIiIiJSAQboRERERERERCrAAJ2IiIiIiIhIBRigExEREREREakAA3QiIiIiIiIiFWCATkRERERERKQCDNCJiIiIiIiIVIABOhEREREREZEKMEAnIiIiIiIiUgEG6EREREREREQqwACdiIiIiIiISAUYoBMRERERERGpAAN0IiIiIiIiIhXQK12AtkgQBABAWVmZouUwGo2oqqpCWVkZDAaDomVp71gX6sB6UA/WhTqwHtSDdaEOrAf1UEtdWL/PW7/fE7U0BugtoLy8HACQlpamcEmIiIiIiKi5ysvLERkZqXQxqB3QCGwO8jmz2Yzc3FyEh4dDo9EoVo6ysjKkpaXh3LlziIiIUKwcxLpQC9aDerAu1IH1oB6sC3VgPaiHWupCEASUl5cjJSUFWi1nB1PLYw96C9BqtUhNTVW6GKKIiAh+yKgE60IdWA/qwbpQB9aDerAu1IH1oB5qqAv2nJM/sRmIiIiIiIiISAUYoBMRERERERGpAAP0NiwwMBCLFy9GYGCg0kVp91gX6sB6UA/WhTqwHtSDdaEOrAf1YF1Qe8UkcUREREREREQqwB50IiIiIiIiIhVggE5ERERERESkAgzQiYiIiIiIiFSAAToRERERERGRCjBAb8OWLVuGTp06ISgoCEOHDsVvv/2mdJHanaeeegoajUbyX/fu3ZUuVpu3ceNGXHHFFUhJSYFGo8GXX34p2S8IAp588kkkJycjODgYkyZNwrFjx5QpbBvnqS7mzJnj9B6ZNm2aMoVto5YsWYLBgwcjPDwcCQkJmDlzJo4cOSI5pqamBvPnz0dsbCzCwsJw7bXXoqCgQKESt13e1MW4ceOc3hN33323QiVum9566y306dMHERERiIiIwPDhw/H999+L+/l+8B9PdcH3A7VHDNDbqFWrVmHBggVYvHgxdu3ahb59+2Lq1KkoLCxUumjtTs+ePZGXlyf+98svvyhdpDavsrISffv2xbJly2T3L126FG+88QaWL1+Obdu2ITQ0FFOnTkVNTY2fS9r2eaoLAJg2bZrkPfLxxx/7sYRt34YNGzB//nxs3boVa9asgdFoxJQpU1BZWSke8+CDD+Lrr7/Gp59+ig0bNiA3NxfXXHONgqVum7ypCwCYN2+e5D2xdOlShUrcNqWmpuL555/Hzp07sWPHDkyYMAFXXXUVDhw4AIDvB3/yVBcA3w/UDgnUJg0ZMkSYP3+++NhkMgkpKSnCkiVLFCxV+7N48WKhb9++ShejXQMgfPHFF+Jjs9ksJCUlCS+++KK4raSkRAgMDBQ+/vhjBUrYfjjWhSAIwq233ipcddVVipSnvSosLBQACBs2bBAEwfL7bzAYhE8//VQ85tChQwIAYcuWLUoVs11wrAtBEISxY8cKf/rTn5QrVDsVHR0tvPPOO3w/qIC1LgSB7wdqn9iD3gbV1dVh586dmDRpkrhNq9Vi0qRJ2LJli4Ila5+OHTuGlJQUZGZm4qabbsLZs2eVLlK7durUKeTn50veH5GRkRg6dCjfHwr5+eefkZCQgG7duuGee+7BpUuXlC5Sm1ZaWgoAiImJAQDs3LkTRqNR8p7o3r07OnbsyPdEC3OsC6sPP/wQcXFx6NWrFxYtWoSqqiolitcumEwmfPLJJ6isrMTw4cP5flCQY11Y8f1A7Y1e6QKQ7128eBEmkwmJiYmS7YmJiTh8+LBCpWqfhg4divfeew/dunVDXl4enn76aYwePRr79+9HeHi40sVrl/Lz8wFA9v1h3Uf+M23aNFxzzTXIyMjAiRMn8Oijj2L69OnYsmULdDqd0sVrc8xmM/785z9j5MiR6NWrFwDLeyIgIABRUVGSY/meaFlydQEAs2fPRnp6OlJSUrBv3z488sgjOHLkCD7//HMFS9v2/P777xg+fDhqamoQFhaGL774AtnZ2dizZw/fD37mqi4Avh+ofWKATtSCpk+fLv7cp08fDB06FOnp6fjPf/6DuXPnKlgyInW44YYbxJ979+6NPn36oHPnzvj5558xceJEBUvWNs2fPx/79+9nLgwVcFUXd955p/hz7969kZycjIkTJ+LEiRPo3Lmzv4vZZnXr1g179uxBaWkp/vvf/+LWW2/Fhg0blC5Wu+SqLrKzs/l+oHaJQ9zboLi4OOh0OqeMowUFBUhKSlKoVAQAUVFR6Nq1K44fP650Udot63uA7w91yszMRFxcHN8jLeC+++7DN998g/Xr1yM1NVXcnpSUhLq6OpSUlEiO53ui5biqCzlDhw4FAL4nfCwgIABdunTBwIEDsWTJEvTt2xevv/463w8KcFUXcvh+oPaAAXobFBAQgIEDB2Lt2rXiNrPZjLVr10rm9JD/VVRU4MSJE0hOTla6KO1WRkYGkpKSJO+PsrIybNu2je8PFTh//jwuXbrE94gPCYKA++67D1988QXWrVuHjIwMyf6BAwfCYDBI3hNHjhzB2bNn+Z7wMU91IWfPnj0AwPdECzObzaitreX7QQWsdSGH7wdqDzjEvY1asGABbr31VgwaNAhDhgzBa6+9hsrKStx2221KF61deeihh3DFFVcgPT0dubm5WLx4MXQ6HW688Uali9amVVRUSFrXT506hT179iAmJgYdO3bEn//8Zzz77LPIyspCRkYGnnjiCaSkpGDmzJnKFbqNclcXMTExePrpp3HttdciKSkJJ06cwMMPP4wuXbpg6tSpCpa6bZk/fz4++ugj/O9//0N4eLg4jzYyMhLBwcGIjIzE3LlzsWDBAsTExCAiIgL3338/hg8fjmHDhilc+rbFU12cOHECH330EWbMmIHY2Fjs27cPDz74IMaMGYM+ffooXPq2Y9GiRZg+fTo6duyI8vJyfPTRR/j555/x448/8v3gZ+7qgu8HareUTiNPLefNN98UOnbsKAQEBAhDhgwRtm7dqnSR2p3rr79eSE5OFgICAoQOHToI119/vXD8+HGli9XmrV+/XgDg9N+tt94qCIJlqbUnnnhCSExMFAIDA4WJEycKR44cUbbQbZS7uqiqqhKmTJkixMfHCwaDQUhPTxfmzZsn5OfnK13sNkXu9QcgrFy5UjymurpauPfee4Xo6GghJCREuPrqq4W8vDzlCt1GeaqLs2fPCmPGjBFiYmKEwMBAoUuXLsJf/vIXobS0VNmCtzG33367kJ6eLgQEBAjx8fHCxIkThdWrV4v7+X7wH3d1wfcDtVcaQRAEfzYIEBEREREREZEzzkEnIiIiIiIiUgEG6EREREREREQq8P8B8kAV7PcsdFcAAAAASUVORK5CYII=", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=1000.0/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dc805b9cda0745648a306a1cffb388f8", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=1000.0/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\miniconda3\\envs\\stabilitySetup\\lib\\site-packages\\numpy\\core\\fromnumeric.py:3464: RuntimeWarning: Mean of empty slice.\n", "  return _methods._mean(a, axis=axis, dtype=dtype,\n", "C:\\Users\\<USER>\\miniconda3\\envs\\stabilitySetup\\lib\\site-packages\\numpy\\core\\_methods.py:192: RuntimeWarning: invalid value encountered in scalar divide\n", "  ret = ret.dtype.type(ret / rcount)\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "70e5f62968004b4cbcb261481b5882c0", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAA+gAAAGQCAYAAAA9TUphAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAPIVJREFUeJzt3Xl8Tdf+//H3iUgiSEJkEDKgiKhKzTGUEs01T7lCqbHctiilSrQ1u4pSdED1lvaaUjW7ala0Ug1Kq4Zqr6lIxJDEGCH794dfzu35JiFUcnbj9Xw89qPN2mvt/VmRtN5n7722xTAMQwAAAAAAwK4c7F0AAAAAAAAgoAMAAAAAYAoEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMA8Ac9evRQUFCQvcswve+//15OTk46efLkQ42fP3++LBaLTpw4YW1r1KiRGjVq9GgKNLETJ07IYrFo/vz5dqth9uzZCggIUGpqqt1qAABkRkAHgMfITz/9pMjISAUGBsrFxUWlSpVS06ZN9f7779v0CwoKksViUXh4eJbHmTt3riwWiywWi/bs2WOz75tvvlGzZs1UqlQpubi4KCAgQK1atdKiRYtybV45FRQUpJYtWz7QmOvXr2v06NH6+uuvc6eoe8gIsRmbi4uLKlSooP79+yshISFT/4SEBL3++usKDg6Wq6urChcurOrVq2v8+PFKSkqy9mvUqJHNcf+4BQcH56i2N998U507d1ZgYGCW+2vVqiWLxaJZs2Y91NxzKikpSd7e3rJYLPryyy/v2z8jHGdsBQsWVIkSJVS3bl2NGDFCp06dytV6zaJHjx66deuW5syZY+9SAAB/4GjvAgAAeWPXrl169tlnFRAQoD59+sjX11enT5/Wd999pxkzZmjAgAE2/V1cXLRt2zbFx8fL19fXZt/ChQvl4uKimzdv2rQvXbpUUVFRCg0N1cCBA1WsWDEdP35cO3bs0Ny5c/X888/n+jz/rLlz5yo9Pd369fXr1zVmzBhJstvV3bFjx6pMmTK6efOmvvnmG82aNUvr1q3TwYMH5erqKkmKi4tT8+bNdfXqVXXt2lXVq1eXJO3Zs0fvvPOOduzYoY0bN1qPWbp0aU2cODHTudzd3e9bz/79+7V582bt2rUry/3Hjh1TXFycgoKCtHDhQr388ssPM+0cGTlypK5fv/7A4zp37qzmzZsrPT1dly9fVlxcnKZPn64ZM2boX//6lzp16pQL1d4VGBioGzduqGDBgrl2jvtxcXFR9+7dNW3aNA0YMEAWi8VutQAA/oeADgCPiQkTJsjd3V1xcXHy8PCw2Xf+/PlM/evVq6e4uDjFxMRo4MCB1vbff/9dO3fuVLt27bRs2TKbMaNHj1ZISIi+++47OTk53fccZmTP0JSdZs2aqUaNGpKkF198UZ6enpo2bZpWrVqlzp07KykpSe3atVOBAgX0ww8/ZLoKPmHCBM2dO9emzd3dXV27dn2oeubNm6eAgADVqVMny/0LFiyQt7e3pk6dqsjISJ04cSJXHhs4ePCgZs2apZEjR2rkyJEPNLZatWqZ5n/y5Ek999xz6t69uypVqqSqVas+ynKtMu6GsLeOHTtq8uTJ2rZtmxo3bmzvcgAA4hZ3AHhs/Pbbb6pcuXKmcC5J3t7emdpcXFzUvn37TLemL168WMWKFVNERESW56hZs2amcJ7dOf6oZcuWKlu2bJb7wsLCrAFVkjZt2qT69evLw8NDRYoUUcWKFTVixIh7Hj+n/vgM+okTJ+Tl5SVJGjNmjPW26NGjR1v7b926VQ0aNFDhwoXl4eGhNm3a6PDhwzbHvHLligYNGqSgoCA5OzvL29tbTZs21b59+x6qxowwdfz4cUnSnDlzdObMGU2bNi3LW9R9fHz01ltvPdS5srJy5Uo1btw426uuixYtUmRkpFq2bCl3d/dce7xh4MCBateunRo0aPBIjhcYGKj58+fr1q1bmjx5ss2+pKQkDRo0SP7+/nJ2dtYTTzyhSZMmWe+2SEtLU/HixdWzZ89Mx01JSZGLi4tef/11Sdk/g37kyBF17NhRXl5eKlSokCpWrKg333zTps+ZM2fUq1cv+fj4yNnZWZUrV9ann36a6Zzvv/++KleuLFdXVxUrVkw1atTI9OdQvXp1FS9eXKtWrXrg7xUAIHdwBR0AHhOBgYGKjY3VwYMH9eSTT+ZozPPPP6/nnntOv/32m8qVKyfpf+ErqyvNgYGB2rJli37//XeVLl36geqLiopSt27dFBcXp5o1a1rbT548qe+++05TpkyRJP38889q2bKlnnrqKY0dO1bOzs769ddf9e233z7Q+XLCy8tLs2bN0ssvv6x27dqpffv2kqSnnnpKkrR582Y1a9ZMZcuW1ejRo3Xjxg29//77qlevnvbt22cN+i+99JK+/PJL9e/fXyEhIbp48aK++eYbHT58WNWqVXvgun777TdJkqenpyRp9erVKlSokCIjI3N8jDt37ujChQuZ2gsVKqTChQtnO+7MmTM6depUtnXv3r1bv/76q+bNmycnJye1b99eCxcufGQfoGRYunSpdu3apcOHD9ssNPdnhYWFqVy5ctq0aZO17fr162rYsKHOnDmjf/zjHwoICNCuXbsUHR2tc+fOafr06SpYsKDatWun5cuXa86cOTYfUq1cuVKpqan3vG3+xx9/VIMGDVSwYEH17dtXQUFB+u2337RmzRpNmDBB0t01BurUqSOLxaL+/fvLy8tLX331lXr37q2UlBQNGjRI0t3HNF599VVFRkZq4MCBunnzpn788Uft3r0702Mm1apVy5XfHQDAQzIAAI+FjRs3GgUKFDAKFChghIWFGW+88YaxYcMG49atW5n6BgYGGi1atDBu375t+Pr6GuPGjTMMwzAOHTpkSDK2b99uzJs3z5BkxMXFWcf961//MiQZTk5OxrPPPmu8/fbbxs6dO407d+7ct77k5GTD2dnZGDJkiE375MmTDYvFYpw8edIwDMN47733DElGYmLiA38PMuZ1L927dzcCAwOtXycmJhqSjFGjRmXqGxoaanh7exsXL160th04cMBwcHAwunXrZm1zd3c3+vXr98D1ZnyPN2/ebCQmJhqnT582lixZYnh6ehqFChUyfv/9d8MwDKNYsWJG1apVc3zchg0bGpKy3P7xj3/cc+zmzZsNScaaNWuy3N+/f3/D39/fSE9PNwzj7s+dJOOHH37Icm7Hjx+3qathw4b3rf/69etGQECAER0dbRiGYWzbts2QZCxduvS+Y48fP25IMqZMmZJtnzZt2hiSjOTkZMMwDGPcuHFG4cKFjV9++cWm3/Dhw40CBQoYp06dMgzDMDZs2JDl96Z58+ZG2bJlM9Uwb948a9szzzxjFC1a1PpzniHj+2gYhtG7d2+jZMmSxoULF2z6dOrUyXB3dzeuX79urb9y5cr3+1YYhmEYffv2NQoVKpSjvgCA3Mct7gDwmGjatKliY2PVunVrHThwQJMnT1ZERIRKlSql1atXZzmmQIEC6tixoxYvXizp7uJw/v7+2d5S3KtXL61fv16NGjXSN998o3HjxqlBgwYqX758tguKZXBzc1OzZs30xRdfyDAMa3tMTIzq1KmjgIAASbLeor9q1Sqbxdzy2rlz57R//3716NFDxYsXt7Y/9dRTatq0qdatW2dt8/Dw0O7du3X27NmHOld4eLi8vLzk7++vTp06qUiRIlqxYoVKlSol6e4t1EWLFn2gYwYFBWnTpk2ZtoyrsNm5ePGiJKlYsWKZ9t2+fVsxMTGKioqy3v7euHFjeXt7a+HChQ9U37288847SktLe+RX5TMUKVJE0t1HE6S7V+sbNGigYsWK6cKFC9YtPDxcd+7c0Y4dOyTdnWuJEiUUExNjPdbly5e1adMmRUVFZXu+xMRE7dixQ7169bL+nGfI+D4ahqFly5apVatWMgzDpo6IiAglJydbH5nw8PDQ77//rri4uPvOtVixYrpx48ZDLbQHAHj0COgA8BipWbOmli9frsuXL+v7779XdHS0rly5osjISB06dCjLMc8//7wOHTqkAwcOaNGiRerUqdM9V3yOiIjQhg0blJSUpB07dqhfv346efKkWrZsed+F4qKionT69GnFxsZKunsr9969e23CTVRUlOrVq6cXX3xRPj4+6tSpk7744os8D+sZ7/+uWLFipn2VKlXShQsXdO3aNUnS5MmTdfDgQfn7+6tWrVoaPXq0/vvf/+b4XB9++KE2bdqkbdu26dChQ/rvf/9rswaAm5ubNUzmVOHChRUeHp5py+lr1v74IUqGjRs3KjExUbVq1dKvv/6qX3/9VcePH9ezzz6rxYsXP5I/oxMnTmjKlCmaMGGCNUg/alevXpUk64cex44d0/r16+Xl5WWzZbyGMOPn2tHRUR06dNCqVaus7xdfvny50tLS7hnQM34W7vXoSWJiopKSkvTxxx9nqiPjufeMOoYNG6YiRYqoVq1aKl++vPr165ftbewZf46s4g4A5sAz6ADwGHJyclLNmjVVs2ZNVahQQT179tTSpUs1atSoTH1r166tcuXKadCgQTp+/HiOX5Xm6uqqBg0aqEGDBipRooTGjBmjr776St27d892TKtWreTq6qovvvhCdevW1RdffCEHBwf9/e9/t/YpVKiQduzYoW3btuk///mP1q9fr5iYGDVu3FgbN25UgQIFHvwbkss6duyoBg0aaMWKFdq4caOmTJmiSZMmafny5WrWrNl9x9eqVctmkbz/Kzg4WPv379etW7eyXKDvUcp47v3y5cuZ9mVcJe/YsWOWY7dv365nn332T51/5MiRKlWqlBo1amR99jw+Pl7S3RB74sQJBQQEyMHh4a9BHDx4UN7e3nJzc5Mkpaenq2nTpnrjjTey7F+hQgXrv3fq1Elz5szRV199pbZt2+qLL75QcHDwn14RPuPDja5du2b7O5SxNkKlSpV09OhRrV27VuvXr9eyZcv00UcfaeTIkdZXBma4fPmyXF1dVahQoT9VHwDg0SCgA8BjLiP4nTt3Lts+nTt31vjx41WpUiWFhobmyjmku1d1W7ZsqaVLl2ratGmKiYlRgwYN5OfnZ9PPwcFBTZo0UZMmTTRt2jT985//1Jtvvqlt27ZZr2o+KtldWQwMDJQkHT16NNO+I0eOqESJEjaLrZUsWVKvvPKKXnnlFZ0/f17VqlXThAkTchTQ76dVq1aKjY3VsmXL1Llz5z99vHvJuMKesYJ8hmvXrmnVqlWKiorKcrG6V199VQsXLvzTAf3UqVP69ddfs1zx/5VXXpF0N3Rm9baCnIiNjdVvv/1m8wq2cuXK6erVqzn62XrmmWdUsmRJxcTEqH79+tq6dWumldj/r4y5HDx4MNs+Xl5eKlq0qO7cuZOjOgoXLqyoqChFRUXp1q1bat++vSZMmKDo6GibV7wdP35clSpVuu/xAAB5g1vcAeAxsW3btixvS854VjqrW7UzvPjiixo1apSmTp16z3Ns2bIly/acnCNDVFSUzp49q08++UQHDhzIdGvwpUuXMo3J+NAg47biR8nV1VXS3dds/VHJkiUVGhqqzz77zGbfwYMHtXHjRjVv3lzS3dXSk5OTbcZ6e3vLz8/vkdX70ksvqWTJkhoyZIh++eWXTPvPnz+v8ePHP5JzlSpVSv7+/tqzZ49N+4oVK3Tt2jX169dPkZGRmbaWLVtq2bJlf3rO48eP14oVK2y2cePGSZLeeOMNrVix4p6r0N/LyZMn1aNHDzk5OWno0KHW9o4dOyo2NlYbNmzINCYpKUm3b9+2fu3g4KDIyEitWbNG//73v3X79u173t4u3Q3fzzzzjD799FOdOnXKZl/G72yBAgXUoUMHLVu2LMsgn5iYaP33jHUCMjg5OSkkJESGYSgtLc1m3759+1S3bt171gcAyDtcQQeAx8SAAQN0/fp1tWvXTsHBwbp165Z27dqlmJgYBQUFZfn+5gyBgYE27/7OTps2bVSmTBm1atVK5cqV07Vr17R582atWbNGNWvWVKtWre57jObNm6to0aJ6/fXXraHkj8aOHasdO3aoRYsWCgwM1Pnz5/XRRx+pdOnSql+//n2P/+uvv2YZVp9++mm1aNEiU3uhQoUUEhKimJgYVahQQcWLF9eTTz6pJ598UlOmTFGzZs0UFham3r17W1+z5u7ubv1+XblyRaVLl1ZkZKSqVq2qIkWKaPPmzYqLi7vvBx45VaxYMa1YsULNmzdXaGiounbtqurVq0u6G8AWL16ssLAwmzHJyclasGBBlsf749XjrLRp00YrVqyQYRjWOwwWLlwoT0/PbMNe69atNXfuXP3nP/+xvq7uYWT1Z5xxtbxmzZpq27Ztjo6zb98+LViwQOnp6UpKSlJcXJyWLVsmi8Wif//739bbxSVp6NChWr16tVq2bKkePXqoevXqunbtmn766Sd9+eWXOnHihEqUKGHtHxUVpffff1+jRo1SlSpVcnSFeubMmapfv76qVaumvn37qkyZMjpx4oT+85//aP/+/ZLuLo63bds21a5dW3369FFISIguXbqkffv2afPmzdYPr5577jn5+vqqXr168vHx0eHDh/XBBx+oRYsWNosJ7t27V5cuXVKbNm1y9D0DAOQBu60fDwDIU1999ZXRq1cvIzg42ChSpIjh5ORkPPHEE8aAAQOMhIQEm745eR1ZVq9ZW7x4sdGpUyejXLlyRqFChQwXFxcjJCTEePPNN42UlJQc19qlSxdDkhEeHp5p35YtW4w2bdoYfn5+hpOTk+Hn52d07tw50yuwshIYGJjt68V69+5tGEbm16wZhmHs2rXLqF69uuHk5JTplWubN2826tWrZxQqVMhwc3MzWrVqZRw6dMi6PzU11Rg6dKhRtWpVo2jRokbhwoWNqlWrGh999NF9683qe3wvZ8+eNV577TWjQoUKhouLi+Hq6mpUr17dmDBhgvWVYYZx79es5eSvBvv27TMkGTt37jQMwzASEhIMR0dH44UXXsh2zPXr1w1XV1ejXbt2NnN7mNes/V8P85q1jM3R0dEoXry4Ubt2bSM6OjrTa84yXLlyxYiOjjaeeOIJw8nJyShRooRRt25d49133830qsL09HTD39/fkGSMHz8+2xr++Jo1wzCMgwcPGu3atTM8PDwMFxcXo2LFisbbb79t0ychIcHo16+f4e/vbxQsWNDw9fU1mjRpYnz88cfWPnPmzDGeeeYZw9PT03B2djbKlStnDB061OZnwDAMY9iwYUZAQIDNq9wAAPZlMYws7ncEAAC4hyZNmsjPz0///ve/7V0KHkJqaqqCgoI0fPhwDRw40N7lAAD+P55BBwAAD+yf//ynYmJirK+bw1/LvHnzVLBgQb300kv2LgUA8AdcQQcAAAAAwAS4gg4AAAAAgAkQ0AEAAAAAMAECOgAAAAAAJkBABwAAAADABBztXQDyVnp6us6ePauiRYvKYrHYuxwAAAAAdmIYhq5cuSI/Pz85OHDt1gwI6I+Zs2fPyt/f395lAAAAADCJ06dPq3Tp0vYuAyKgP3aKFi0q6e4voZubm52rAQAAAGAvKSkp8vf3t2YE2B8B/TGTcVu7m5sbAR0AAAAAj76aCA8aAAAAAABgAgR0AAAAAABMgIAOAAAAAIAJENABAAAAADABAjoAAAAAACZAQAcAAAAAwAQI6AAAAAAAmAABHQAAAAAAEyCgAwAAAABgAgR0AAAAAABMgIAOAAAAAIAJENABAAAAADABAjoAAAAAACZAQAcAAAAAwAQI6AAAAAAAmAABHQAAAAAAEyCgAwAAAABgAgR0AAAAAABMgIAOAAAAAIAJENABAAAAADABAjoAAAAAACZAQAcAAAAAwAQI6AAAAAAAmAABHQAAAAAAEyCgAwAAAABgAgR0AAAAAABMgIAOAAAAAIAJENABAAAAADABAjoAAAAAACZAQAcAAAAAwAQI6AAAAAAAmAABHQAAAAAAEyCgAwAAAABgAgR0AAAAAABMgIAOAAAAAIAJENBz2YcffqigoCC5uLiodu3a+v777+/Zf+nSpQoODpaLi4uqVKmidevWZdv3pZdeksVi0fTp0x9x1QAAAACAvEZAz0UxMTEaPHiwRo0apX379qlq1aqKiIjQ+fPns+y/a9cude7cWb1799YPP/ygtm3bqm3btjp48GCmvitWrNB3330nPz+/3J4GAAAAACAPENBz0bRp09SnTx/17NlTISEhmj17tlxdXfXpp59m2X/GjBn629/+pqFDh6pSpUoaN26cqlWrpg8++MCm35kzZzRgwAAtXLhQBQsWzIupAAAAAAByGQE9l9y6dUt79+5VeHi4tc3BwUHh4eGKjY3NckxsbKxNf0mKiIiw6Z+enq4XXnhBQ4cOVeXKle9bR2pqqlJSUmw2AAAAAID5ENBzyYULF3Tnzh35+PjYtPv4+Cg+Pj7LMfHx8fftP2nSJDk6OurVV1/NUR0TJ06Uu7u7dfP393/AmQAAAAAA8gIB/S9k7969mjFjhubPny+LxZKjMdHR0UpOTrZup0+fzuUqAQAAAAAPg4CeS0qUKKECBQooISHBpj0hIUG+vr5ZjvH19b1n/507d+r8+fMKCAiQo6OjHB0ddfLkSQ0ZMkRBQUFZHtPZ2Vlubm42GwAAAADAfAjoucTJyUnVq1fXli1brG3p6enasmWLwsLCshwTFhZm01+SNm3aZO3/wgsv6Mcff9T+/futm5+fn4YOHaoNGzbk3mQAAAAAALnO0d4F5GeDBw9W9+7dVaNGDdWqVUvTp0/XtWvX1LNnT0lSt27dVKpUKU2cOFGSNHDgQDVs2FBTp05VixYttGTJEu3Zs0cff/yxJMnT01Oenp425yhYsKB8fX1VsWLFvJ0cAAAAAOCRIqDnoqioKCUmJmrkyJGKj49XaGio1q9fb10I7tSpU3Jw+N9NDHXr1tWiRYv01ltvacSIESpfvrxWrlypJ5980l5TAAAAAADkEYthGIa9i0DeSUlJkbu7u5KTk3keHQAAAHiMkQ3Mh2fQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoOeyDz/8UEFBQXJxcVHt2rX1/fff37P/0qVLFRwcLBcXF1WpUkXr1q2z7ktLS9OwYcNUpUoVFS5cWH5+furWrZvOnj2b29MAAAAAAOQyAnouiomJ0eDBgzVq1Cjt27dPVatWVUREhM6fP59l/127dqlz587q3bu3fvjhB7Vt21Zt27bVwYMHJUnXr1/Xvn379Pbbb2vfvn1avny5jh49qtatW+fltAAAAAAAucBiGIZh7yLyq9q1a6tmzZr64IMPJEnp6eny9/fXgAEDNHz48Ez9o6KidO3aNa1du9baVqdOHYWGhmr27NlZniMuLk61atXSyZMnFRAQcN+aUlJS5O7uruTkZLm5uT3kzAAAAAD81ZENzIcr6Lnk1q1b2rt3r8LDw61tDg4OCg8PV2xsbJZjYmNjbfpLUkRERLb9JSk5OVkWi0UeHh5Z7k9NTVVKSorNBgAAAAAwHwJ6Lrlw4YLu3LkjHx8fm3YfHx/Fx8dnOSY+Pv6B+t+8eVPDhg1T586ds/3Ea+LEiXJ3d7du/v7+DzEbAAAAAEBuI6D/RaWlpaljx44yDEOzZs3Ktl90dLSSk5Ot2+nTp/OwSgAAAABATjnau4D8qkSJEipQoIASEhJs2hMSEuTr65vlGF9f3xz1zwjnJ0+e1NatW+/5vIizs7OcnZ0fchYAAAAAgLzCFfRc4uTkpOrVq2vLli3WtvT0dG3ZskVhYWFZjgkLC7PpL0mbNm2y6Z8Rzo8dO6bNmzfL09MzdyYAAAAAAMhTXEHPRYMHD1b37t1Vo0YN1apVS9OnT9e1a9fUs2dPSVK3bt1UqlQpTZw4UZI0cOBANWzYUFOnTlWLFi20ZMkS7dmzRx9//LGku+E8MjJS+/bt09q1a3Xnzh3r8+nFixeXk5OTfSYKAAAAAPjTCOi5KCoqSomJiRo5cqTi4+MVGhqq9evXWxeCO3XqlBwc/ncTQ926dbVo0SK99dZbGjFihMqXL6+VK1fqySeflCSdOXNGq1evliSFhobanGvbtm1q1KhRnswLAAAAAPDo8R70xwzvOgQAAAAgkQ3MiGfQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATOCxCuipqan2LgEAAAAAgCzl64D+1VdfqXv37ipbtqwKFiwoV1dXubm5qWHDhpowYYLOnj1r7xIBAAAAAJCUTwP6ihUrVKFCBfXq1UuOjo4aNmyYli9frg0bNuiTTz5Rw4YNtXnzZpUtW1YvvfSSEhMT7V0yAAAAAOAxZzEMw7B3EY9aWFiY3nrrLTVr1kwODtl/BnHmzBm9//778vHx0WuvvZaHFdpPSkqK3N3dlZycLDc3N3uXAwAAAMBOyAbmky8DOrLHLyEAAAAAiWxgRvnyFvd7uXbtmlJSUuxdBgAAAAAANh6bgH7o0CHVqFFDRYsWVbFixVSlShXt2bPH3mUBAAAAACDpMQro//jHP9S/f39dvXpVFy9eVPv27dW9e3d7lwUAAAAAgKR8HNDbtGmjM2fOWL9OTExU69at5erqKg8PDzVv3lwJCQl2rBAAAAAAgP9xtHcBuaVr165q3Lix+vXrpwEDBqh///6qXLmyGjZsqLS0NG3dulVDhgyxd5kAAAAAAEjK56u4Jycna9iwYfrhhx80e/ZsOTo66uuvv9adO3dUr1491axZ094l5jlWagQAAAAgkQ3MKN9eQZckd3d3zZ49W9988426d++upk2baty4cXJ1dbV3aQAAAAAA2Mi3z6BL0qVLl7R3715VqVJFe/fulZubm55++mmtW7fO3qUBAAAAAGAj3wb0RYsWqXTp0mrRooUCAwP11VdfadSoUVq1apUmT56sjh07skgcAAAAAMA08m1Aj46O1qeffqr4+Hht2bJFb7/9tiQpODhYX3/9tZo2baqwsDA7VwkAAAAAwF35NqBfvXpVFStWlCSVK1dO169ft9nfp08ffffdd/YoDQAAAACATPLtInHdu3dXixYt1KhRI+3Zs0cvvPBCpj7e3t52qAwAAAAAgMzy9WvW1qxZoyNHjqhq1ap67rnn7F2OKfAqBQAAAAAS2cCM8nVAR2b8EgIAAACQyAZmlC+fQV+yZEmO+54+fVrffvttLlYDAAAAAMD95cuAPmvWLFWqVEmTJ0/W4cOHM+1PTk7WunXr9Pzzz6tatWq6ePGiHaoEAAAAAOB/8uUicdu3b9fq1av1/vvvKzo6WoULF5aPj49cXFx0+fJlxcfHq0SJEurRo4cOHjwoHx8fe5cMAAAAAHjM5csr6JLUunVrbdq0SQkJCfr888/Vv39/denSRaNHj9bu3bt19uxZvfPOO7kezj/88EMFBQXJxcVFtWvX1vfff3/P/kuXLlVwcLBcXFxUpUoVrVu3zma/YRgaOXKkSpYsqUKFCik8PFzHjh3LzSkAAAAAAPJAvryC/kclSpRQ27Zt7XLumJgYDR48WLNnz1bt2rU1ffp0RURE6OjRo1m+4m3Xrl3q3LmzJk6cqJYtW2rRokVq27at9u3bpyeffFKSNHnyZM2cOVOfffaZypQpo7ffflsRERE6dOiQXFxc8nqKAAAAAIBHhFXcc1Ht2rVVs2ZNffDBB5Kk9PR0+fv7a8CAARo+fHim/lFRUbp27ZrWrl1rbatTp45CQ0M1e/ZsGYYhPz8/DRkyRK+//rqku8/T+/j4aP78+erUqdN9a2KlRgAAAAAS2cCM8u0t7vZ269Yt7d27V+Hh4dY2BwcHhYeHKzY2NssxsbGxNv0lKSIiwtr/+PHjio+Pt+nj7u6u2rVrZ3vM1NRUpaSk2GwAAAAAAPMhoOeSCxcu6M6dO5mecffx8VF8fHyWY+Lj4+/ZP+OfD3LMiRMnyt3d3br5+/s/1HwAAAAAALmLgJ7PRUdHKzk52bqdPn3a3iUBAAAAALJAQM8lJUqUUIECBZSQkGDTnpCQIF9f3yzH+Pr63rN/xj8f5JjOzs5yc3Oz2QAAAAAA5pNvA3pISIguXbpk/fqVV17RhQsXrF+fP39erq6uuXZ+JycnVa9eXVu2bLG2paena8uWLQoLC8tyTFhYmE1/Sdq0aZO1f5kyZeTr62vTJyUlRbt37872mAAAAACAv4Z8G9CPHDmi27dvW79esGCBzQJphmHo5s2buVrD4MGDNXfuXH322Wc6fPiwXn75ZV27dk09e/aUJHXr1k3R0dHW/gMHDtT69es1depUHTlyRKNHj9aePXvUv39/SZLFYtGgQYM0fvx4rV69Wj/99JO6desmPz8/u71KDgAAAADwaOT796BnyOptchaLJVfPGRUVpcTERI0cOVLx8fEKDQ3V+vXrrYu8nTp1Sg4O//uMpG7dulq0aJHeeustjRgxQuXLl9fKlSut70CXpDfeeEPXrl1T3759lZSUpPr162v9+vW8Ax0AAAAA/uLy7XvQHRwcFB8fL29vb0lS0aJFdeDAAZUtW1bS3ee2/fz8dOfOHXuWmed41yEAAAAAiWxgRvn2FneLxZLpCnluXzEHAAAAAOBh5dtb3A3DUJMmTeToeHeKN27cUKtWreTk5CRJNs+nAwAAAABgb/k2oI8aNcrm6zZt2mTq06FDh7wqBwAAAACAe8q3z6AjazxnAgAAAEAiG5hRvn0G/ebNm1q9erWuXLmSaV9KSopWr16t1NRUO1QGAAAAAEBm+Tagz5kzRzNmzFDRokUz7XNzc9PMmTM1d+5cO1QGAAAAAEBm+TagL1y4UIMGDcp2/6BBg/T555/nXUEAAAAAANxDvg3ox44dU9WqVbPd/9RTT+nYsWN5WBEAAAAAANnLtwH99u3bSkxMzHZ/YmIir1oDAAAAAJhGvg3olStX1ubNm7Pdv3HjRlWuXDkPKwIAAAAAIHv5NqD36tVL48aN09q1azPtW7NmjSZMmKBevXrZoTIAAAAAADJztHcBuaVv377asWOHWrdureDgYFWsWFGSdOTIEf3yyy/q2LGj+vbta+cqAQAAAAC4K99eQZekBQsWaMmSJSpfvrx++eUXHT16VBUrVtTixYu1ePFie5cHAAAAAICVxTAMw95FIO+kpKTI3d1dycnJcnNzs3c5AAAAAOyEbGA++fYKenp6uiZNmqR69eqpZs2aGj58uG7cuGHvsgAAAAAAyFK+DegTJkzQiBEjVKRIEZUqVUozZsxQv3797F0WAAAAAABZyrcB/fPPP9dHH32kDRs2aOXKlVqzZo0WLlyo9PR0e5cGAAAAAEAm+Tagnzp1Ss2bN7d+HR4eLovForNnz9qxKgAAAAAAspZvA/rt27fl4uJi01awYEGlpaXZqSIAAAAAALKXb9+DbhiGevToIWdnZ2vbzZs39dJLL6lw4cLWtuXLl9ujPAAAAAAAbOTbgN69e/dMbV27drVDJQAAAAAA3F++Dejz5s2zdwkAAAAAAORYvn0GHQAAAACAvxICOgAAAAAAJkBABwAAAADABAjoAAAAAACYAAEdAAAAAAATIKADAAAAAGACBHQAAAAAAEyAgA4AAAAAgAkQ0AEAAAAAMAECOgAAAAAAJkBABwAAAADABAjoAAAAAACYAAEdAAAAAAATIKADAAAAAGACBHQAAAAAAEyAgA4AAAAAgAkQ0AEAAAAAMAECOgAAAAAAJkBABwAAAADABAjoAAAAAACYAAEdAAAAAAATIKADAAAAAGACBHQAAAAAAEyAgJ4LLl26pC5dusjNzU0eHh7q3bu3rl69es8xN2/eVL9+/eTp6akiRYqoQ4cOSkhIsO4/cOCAOnfuLH9/fxUqVEiVKlXSjBkzcnsqAAAAAIA8QkDPBV26dNHPP/+sTZs2ae3atdqxY4f69u17zzGvvfaa1qxZo6VLl2r79u06e/as2rdvb92/d+9eeXt7a8GCBfr555/15ptvKjo6Wh988EFuTwcAAAAAkAcshmEY9i4iPzl8+LBCQkIUFxenGjVqSJLWr1+v5s2b6/fff5efn1+mMcnJyfLy8tKiRYsUGRkpSTpy5IgqVaqk2NhY1alTJ8tz9evXT4cPH9bWrVtzXF9KSorc3d2VnJwsNze3h5ghAAAAgPyAbGA+XEF/xGJjY+Xh4WEN55IUHh4uBwcH7d69O8sxe/fuVVpamsLDw61twcHBCggIUGxsbLbnSk5OVvHixe9ZT2pqqlJSUmw2AAAAAID5ENAfsfj4eHl7e9u0OTo6qnjx4oqPj892jJOTkzw8PGzafXx8sh2za9cuxcTE3PfW+YkTJ8rd3d26+fv753wyAAAAAIA8Q0DPoeHDh8tisdxzO3LkSJ7UcvDgQbVp00ajRo3Sc889d8++0dHRSk5Otm6nT5/OkxoBAAAAAA/G0d4F/FUMGTJEPXr0uGefsmXLytfXV+fPn7dpv337ti5duiRfX98sx/n6+urWrVtKSkqyuYqekJCQacyhQ4fUpEkT9e3bV2+99dZ963Z2dpazs/N9+wEAAAAA7IuAnkNeXl7y8vK6b7+wsDAlJSVp7969ql69uiRp69atSk9PV+3atbMcU716dRUsWFBbtmxRhw4dJElHjx7VqVOnFBYWZu33888/q3HjxurevbsmTJjwCGYFAAAAADALVnHPBc2aNVNCQoJmz56ttLQ09ezZUzVq1NCiRYskSWfOnFGTJk30+eefq1atWpKkl19+WevWrdP8+fPl5uamAQMGSLr7rLl097b2xo0bKyIiQlOmTLGeq0CBAjn64CADKzUCAAAAkMgGZsQV9FywcOFC9e/fX02aNJGDg4M6dOigmTNnWvenpaXp6NGjun79urXtvffes/ZNTU1VRESEPvroI+v+L7/8UomJiVqwYIEWLFhgbQ8MDNSJEyfyZF4AAAAAgNzDFfTHDJ+SAQAAAJDIBmbEKu4AAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQAQAAAAAwAQI6AAAAAAAmQEAHAAAAAMAECOgAAAAAAJgAAR0AAAAAABMgoAMAAAAAYAIEdAAAAAAATICADgAAAACACRDQc8GlS5fUpUsXubm5ycPDQ71799bVq1fvOebmzZvq16+fPD09VaRIEXXo0EEJCQlZ9r148aJKly4ti8WipKSkXJgBAAAAACCvEdBzQZcuXfTzzz9r06ZNWrt2rXbs2KG+ffvec8xrr72mNWvWaOnSpdq+fbvOnj2r9u3bZ9m3d+/eeuqpp3KjdAAAAACAnVgMwzDsXUR+cvjwYYWEhCguLk41atSQJK1fv17NmzfX77//Lj8/v0xjkpOT5eXlpUWLFikyMlKSdOTIEVWqVEmxsbGqU6eOte+sWbMUExOjkSNHqkmTJrp8+bI8PDxyXF9KSorc3d2VnJwsNze3PzdZAAAAAH9ZZAPz4Qr6IxYbGysPDw9rOJek8PBwOTg4aPfu3VmO2bt3r9LS0hQeHm5tCw4OVkBAgGJjY61thw4d0tixY/X555/LwSFnf3SpqalKSUmx2QAAAAAA5kNAf8Ti4+Pl7e1t0+bo6KjixYsrPj4+2zFOTk6ZroT7+PhYx6Smpqpz586aMmWKAgICclzPxIkT5e7ubt38/f0fbEIAAAAAgDxBQM+h4cOHy2Kx3HM7cuRIrp0/OjpalSpVUteuXR94XHJysnU7ffp0LlUIAAAAAPgzHO1dwF/FkCFD1KNHj3v2KVu2rHx9fXX+/Hmb9tu3b+vSpUvy9fXNcpyvr69u3bqlpKQkm6voCQkJ1jFbt27VTz/9pC+//FKSlLF0QIkSJfTmm29qzJgxWR7b2dlZzs7OOZkiAAAAAMCOCOg55OXlJS8vr/v2CwsLU1JSkvbu3avq1atLuhuu09PTVbt27SzHVK9eXQULFtSWLVvUoUMHSdLRo0d16tQphYWFSZKWLVumGzduWMfExcWpV69e2rlzp8qVK/dnpwcAAAAAsDMC+iNWqVIl/e1vf1OfPn00e/ZspaWlqX///urUqZN1BfczZ86oSZMm+vzzz1WrVi25u7urd+/eGjx4sIoXLy43NzcNGDBAYWFh1hXc/28Iv3DhgvV8D7KKOwAAAADAnAjouWDhwoXq37+/mjRpIgcHB3Xo0EEzZ8607k9LS9PRo0d1/fp1a9t7771n7ZuamqqIiAh99NFH9igfAAAAAGAHvAf9McO7DgEAAABIZAMzYhV3AAAAAABMgIAOAAAAAIAJENABAAAAADABAjoAAAAAACZAQAcAAAAAwAQI6AAAAAAAmAABHQAAAAAAEyCgAwAAAABgAgR0AAAAAABMgIAOAAAAAIAJENABAAAAADABAjoAAAAAACZAQAcAAAAAwAQI6AAAAAAAmAABHQAAAAAAEyCgAwAAAABgAgR0AAAAAABMgIAOAAAAAIAJENABAAAAADABAjoAAAAAACZAQAcAAAAAwAQI6AAAAAAAmAABHQAAAAAAEyCgAwAAAABgAgR0AAAAAABMgIAOAAAAAIAJENABAAAAADABAjoAAAAAACZAQAcAAAAAwAQI6AAAAAAAmAABHQAAAAAAEyCgAwAAAABgAgR0AAAAAABMgIAOAAAAAIAJONq7AOQtwzAkSSkpKXauBAAAAIA9ZWSCjIwA+yOgP2auXLkiSfL397dzJQAAAADM4MqVK3J3d7d3GZBkMfi45LGSnp6us2fPqmjRorJYLPYuB7CLlJQU+fv76/Tp03Jzc7N3OQAAO+D/BcDdK+dXrlyRn5+fHBx4+tkMuIL+mHFwcFDp0qXtXQZgCm5ubvylDAAec/y/AI87rpybCx+TAAAAAABgAgR0AAAAAABMgIAO4LHj7OysUaNGydnZ2d6lAADshP8XADAjFokDAAAAAMAEuIIOAAAAAIAJENABAAAAADABAjoAAAAAACZAQAcAAICpWSwWrVy50t5l2AgKCtL06dPtXQaAfIaADuAvLzExUS+//LICAgLk7OwsX19fRURE6Ntvv5V09y9RFotFS5YsyTS2cuXKslgsmj9/vrXtwIEDat26tby9veXi4qKgoCBFRUXp/PnzeTUlAHis9OjRQ23bts12/7lz59SsWTNJ0okTJ2SxWLR///5crWn06NEKDQ3Ndn9cXJz69u1r/dqMHyIA+OtxtHcBAPBndejQQbdu3dJnn32msmXLKiEhQVu2bNHFixetffz9/TVv3jx16tTJ2vbdd98pPj5ehQsXtrYlJiaqSZMmatmypTZs2CAPDw+dOHFCq1ev1rVr1/J0XgCAu3x9fe1dQiZeXl72LgFAPsQVdAB/aUlJSdq5c6cmTZqkZ599VoGBgapVq5aio6PVunVra78uXbpo+/btOn36tLXt008/VZcuXeTo+L/PKr/99lslJyfrk08+0dNPP60yZcro2Wef1XvvvacyZcrk6dwAAHf98ep0xn+Ln376aVksFjVq1EiSlJ6errFjx6p06dJydnZWaGio1q9fbz3GrVu31L9/f5UsWVIuLi4KDAzUxIkTH7qmP97iHhQUJElq166dLBaL9WtJWrVqlapVqyYXFxeVLVtWY8aM0e3btx/6vADyNwI6gL+0IkWKqEiRIlq5cqVSU1Oz7efj46OIiAh99tlnkqTr168rJiZGvXr1sunn6+ur27dva8WKFTIMI1drBwA8uO+//16StHnzZp07d07Lly+XJM2YMUNTp07Vu+++qx9//FERERFq3bq1jh07JkmaOXOmVq9erS+++EJHjx7VwoULbYL0nxEXFydJmjdvns6dO2f9eufOnerWrZsGDhyoQ4cOac6cOZo/f74mTJjwSM4LIP8hoAP4S3N0dNT8+fP12WefycPDQ/Xq1dOIESP0448/Zurbq1cvzZ8/X4Zh6Msvv1S5cuUyPV9Yp04djRgxQs8//7xKlCihZs2aacqUKUpISMijGQEA7iXj1nJPT0/5+vqqePHikqR3331Xw4YNU6dOnVSxYkVNmjRJoaGh1qvcp06dUvny5VW/fn0FBgaqfv366ty58yOtycPDQ76+vtavx4wZo+HDh6t79+4qW7asmjZtqnHjxmnOnDmP5LwA8h8COoC/vA4dOujs2bNavXq1/va3v+nrr79WtWrVbBZ+k6QWLVro6tWr2rFjhz799NNMV88zTJgwQfHx8Zo9e7YqV66s2bNnKzg4WD/99FMezAYA8KBSUlJ09uxZ1atXz6a9Xr16Onz4sKS7C9Ht379fFStW1KuvvqqNGzfmel0HDhzQ2LFjrXd7FSlSRH369NG5c+d0/fr1XD8/gL8eAjqAfMHFxUVNmzbV22+/rV27dqlHjx4aNWqUTR9HR0e98MILGjVqlHbv3q0uXbpkezxPT0/9/e9/17vvvqvDhw/Lz89P7777bm5PAwCQS6pVq6bjx49r3LhxunHjhjp27KjIyMhcPefVq1c1ZswY7d+/37r99NNPOnbsmFxcXHL13AD+mgjoAPKlkJCQLFdd79Wrl7Zv3642bdqoWLFiOTqWk5OTypUrxyruAGACTk5OkqQ7d+5Y29zc3OTn52d9vWaGb7/9ViEhITb9oqKiNHfuXMXExGjZsmW6dOnSI6mrYMGCNjVJdz8UOHr0qJ544olMm4MDfw0HkBmvWQPwl3bx4kX9/e9/V69evfTUU0+paNGi2rNnjyZPnqw2bdpk6l+pUiVduHBBrq6uWR5v7dq1WrJkiTp16qQKFSrIMAytWbNG69at07x583J7OgDw2EpOTs70bnNPT0/5+/vbtHl7e6tQoUJav369SpcuLRcXF7m7u2vo0KEaNWqUdX2RefPmaf/+/Vq4cKEkadq0aSpZsqSefvppOTg4aOnSpfL19ZWHh0e2Nd24cSNTTUWLFlW5cuUy9Q0KCtKWLVtUr149OTs7q1ixYho5cqRatmypgIAARUZGysHBQQcOHNDBgwc1fvz4h/o+AcjfCOgA/tKKFCmi2rVr67333tNvv/2mtLQ0+fv7q0+fPhoxYkSWYzw9PbM9XkhIiFxdXTVkyBCdPn1azs7OKl++vD755BO98MILuTUNAHjsff3113r66adt2nr37q1PPvnEps3R0VEzZ87U2LFjNXLkSDVo0EBff/21Xn31VSUnJ2vIkCE6f/68QkJCtHr1apUvX17S3WA9efJkHTt2TAUKFFDNmjW1bt26e17J/uWXXzLV1KRJE23evDlT36lTp2rw4MGaO3euSpUqpRMnTigiIkJr167V2LFjNWnSJBUsWFDBwcF68cUXH/bbBCCfsxi8RwgAAAAAALvj4RcAAAAAAEyAgA4AAAAAgAkQ0AEAAAAAMAECOgAAAAAAJkBABwAAAADABAjoAAAAAACYAAEdAAAAAAATIKADAAAAAGACBHQAAAAAAEyAgA4AAAAAgAkQ0AEAAAAAMAECOgAAAAAAJkBABwAAAADABAjoAAAAAACYAAEdAAAAAAATIKADAAAAAGACBHQAAAAAAEyAgA4AAAAAgAkQ0AEAAAAAMAECOgAAAAAAJkBABwAAAADABAjoAAAAAACYwP8DYCOs6xh21j0AAAAASUVORK5CYII=' width=1000.0/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["total_diff nan\n", "total device diff nan\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\miniconda3\\envs\\stabilitySetup\\lib\\site-packages\\numpy\\core\\fromnumeric.py:3464: RuntimeWarning: Mean of empty slice.\n", "  return _methods._mean(a, axis=axis, dtype=dtype,\n", "C:\\Users\\<USER>\\miniconda3\\envs\\stabilitySetup\\lib\\site-packages\\numpy\\core\\_methods.py:192: RuntimeWarning: invalid value encountered in scalar divide\n", "  ret = ret.dtype.type(ret / rcount)\n"]}], "source": ["%matplotlib widget\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import numpy_indexed as npi\n", "from labellines import labelLines\n", "from matplotlib.font_manager import FontProperties\n", "import os\n", "import sys\n", "import re\n", "np.set_printoptions(threshold=sys.maxsize)\n", "from matplotlib.cbook import boxplot_stats\n", "\n", "def get_outlier_indices(data):\n", "    data = np.asarray(data)\n", "    q1 = np.percentile(data, 25)\n", "    q3 = np.percentile(data, 75)\n", "    iqr = q3 - q1\n", "\n", "    lower_bound = q1 - 1.5 * iqr\n", "    upper_bound = q3 + 1.5 * iqr\n", "\n", "    outlier_mask = (data < lower_bound) | (data > upper_bound)\n", "    return np.where(outlier_mask)[0]\n", "\n", "def plot_difference(litos_directory = \"\", ss_file_path = \"\", plot_range = [], time_limit = -1, plot_size = (12,10), remove_outliers = False):\n", "\t# litos_directory = \"\"\n", "\t# litos_directory = r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Mar-4-2025 4 device test\\2025-03-04-- Litos New PCB 4 Device comparison\\devices\\device_2\"\n", "\tall_files = []\n", "\n", "\tif litos_directory != \"\":\n", "\t\tfor root, dirs, files in os.walk(litos_directory):\n", "\t\t\tfor file in files:\n", "\t\t\t\tif file.endswith('.csv'):\n", "\t\t\t\t\tfile_path = os.path.join(root, file)\n", "\t\t\t\t\tall_files.append(file_path)\n", "\n", "\tlitos_pce = []\n", "\tlitos_V = []\n", "\tlitos_mA = []\n", "\tlitos_time = []\n", "\n", "\t# Process each file\n", "\tfor file_path in all_files:\n", "\t\tif \"_2_0\" in file_path:\n", "\t\t\ttry:\n", "\t\t\t\tdata = np.genfromtxt(\n", "\t\t\t\t\tfile_path,\n", "\t\t\t\t\tdelimiter=',',\n", "\t\t\t\t\tcomments='#',\n", "\t\t\t\t\tskip_header=0,\n", "\t\t\t\t\tencoding='cp1252'  # Specify the correct encoding\n", "\t\t\t\t)\n", "\t\t\t\tpce = ((data[:, 1] * (data[:, 2]/1000)) / (0.1*0.128))*100\n", "\t\t\t\t# data_with_pce= np.column_stack((data, pce))\n", "\t\t\t\tlitos_V.append(data[:, 1])\n", "\t\t\t\tlitos_mA.append(data[:, 2]/0.128)\n", "\t\t\t\tlitos_time.append(data[:,0])\n", "\t\t\t\tlitos_pce.append(pce)\n", "\t\t\texcept Exception as e:\n", "\t\t\t\tprint(f\"Error processing {file_path}: {e}\")\n", "\t# pce = []\n", "\t# for file_path in all_files:\n", "\t# \tif \"_2_0\" in file_path:\n", "\t# \t\ttry:\n", "\t# \t\t\twith open(file_path, 'r', encoding='cp1252') as f:\n", "\t# \t\t\t\tall_lines = f.readlines()\n", "\n", "\t# \t\t\tcomment_lines = [line.strip() for line in all_lines if \"PCE\" in line.lstrip()]\n", "\t# \t\t\tpce.append([float(re.search(r'([\\d\\.]+)', s).group(1)) for s in comment_lines if re.search(r'([\\d\\.]+)', s)][0])\n", "\n", "\t# \t\texcept Exception as e:\n", "\t# \t\t\tprint(f\"Error processing {file_path}: {e}\")\n", "\n", "\tlitos_pce = np.array(litos_pce[::-1])\n", "\tlitos_time = np.array(litos_time[::-1])\n", "\tlitos_mA = np.array(litos_mA[::-1])\n", "\tlitos_V = np.array(litos_V[::-1])\n", "\tlitos_time = litos_time/60\n", "\tif litos_directory:\n", "\t\tmax_time = np.max(litos_time[0])\n", "\t\tif max_time > time_limit and time_limit > 0:\n", "\t\t\tend_idx = np.searchsorted(litos_time[0,:], time_limit)\n", "\n", "\t\t\tlitos_time = litos_time[:, :end_idx]\n", "\t\t\tlitos_pce = litos_pce[:, :end_idx]\n", "\t\t\tlitos_mA = litos_mA[:, :end_idx]\n", "\t\t\tlitos_V = litos_V[:, :end_idx]\n", "\telse:\n", "\t\tlitos_mA = [0]\n", "\t\tlitos_V = [0]\n", "\t\tlitos_pce = [0]\n", "\n", "\n", "\tdata = []\n", "\tmA_density = []\n", "\tv = []\n", "\t# ss_file_path = \"\"\n", "\t# ss_file_path = r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Mar-4-2025 4 device test\\Mar-04-2025 16_42_19__4 Device First Test\\Mar-04-2025_16-44-53__4 Device First Test__ID2__mppt.csv\"\n", "\tif ss_file_path != \"\":\n", "\t\tarr = np.loadtxt(ss_file_path, delimiter=\",\", dtype=str)\n", "\t\theader_row = np.where(arr == \"Time\")[0][0]\n", "\n", "\t\tmeta_data = {}\n", "\t\tfor data in arr[:header_row, :2]:\n", "\t\t\tmeta_data[data[0]] = data[1]\n", "\n", "\t\theaders = arr[header_row, :]\n", "\t\tarr = arr[header_row + 1 :, :]\n", "\n", "\t\theader_dict = {value: index for index, value in enumerate(headers)}\n", "\t\ttime = np.array(arr[:, header_dict[\"Time\"]]).astype(\"float\")\n", "\t\ttime /= 60\n", "\n", "\t\tpixel_V = arr[:, 1::2][:, 0:8].astype(float)\n", "\t\tpixel_mA = arr[:, 2::2][:, 0:8].astype(float)\n", "\n", "\t\tcell_area = float(meta_data[\"Cell Area (mm^2)\"])\n", "\t\tmA_density = pixel_mA/cell_area\n", "\n", "\t\tv = pixel_V\n", "\n", "\t\tdata = ((pixel_V*pixel_mA/1000) / (0.1*cell_area))*100\n", "\n", "\t\t# calculation in minutes\n", "\t\tmax_time = time[-1]\n", "\t\tif max_time > time_limit and time_limit > 0:\n", "\t\t\tend_idx = np.searchsorted(time, time_limit)\n", "\t\t\ttime = time[:end_idx]\n", "\t\t\tdata = data[:end_idx,:]\n", "\t\t\tmA_density = mA_density[:end_idx,:]\n", "\t\t\tv = pixel_V[:end_idx,:]\n", "\ttime_label = \"Time (min)\"\n", "\tif max(time) > 60:\n", "\t\ttime /= 60\n", "\t\tlitos_time /= 60\n", "\t\ttime_label = \"Time (hr)\"\n", "\n", "\n", "\n", "\taverage_litos = []\n", "\taverage_sms = []\n", "\n", "\tplt.figure(figsize=plot_size)\n", "\tbottom = min(np.min(litos_pce), np.min(data))*0.95\n", "\ttop = max(np.max(litos_pce), np.max(data))*1.05\n", "\n", "\tplt.ylim(bottom = 0, top = top)\n", "\tplt.xlabel(time_label)\n", "\tplt.grid()\n", "\tcolors = plt.rcParams['axes.prop_cycle'].by_key()['color']\n", "\n", "\tplt.ylabel('PCE (%)')\n", "\tplt.subplots_adjust(left=0.086,\n", "\t\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\t\tright=0.844,\n", "\t\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\t\thspace=0.2)\n", "\t# data = np.roll(data, shift=4, axis=1)\n", "\tfor i in plot_range:\n", "\t\tif ss_file_path:\n", "\t\t\tplt.plot(time,\n", "\t\t\t\t\t data[:,i],\n", "\t\t\t\t\t color=colors[i % len(colors)],\n", "\t\t\t\t\t label = \"SMS\"+ str(i+1))\n", "\t\tif litos_directory:\n", "\t\t\tplt.plot(litos_time[i],\n", "\t\t\t\t\t litos_pce[i],\n", "                     linestyle='--',\n", "\t\t\t\t\t color=colors[i % len(colors)],\n", "\t\t\t\t\t label = \"CAS\")\n", "\t\tif ss_file_path and litos_directory:\n", "\t\t\tsecond_half = data[:,i][len(data[:,i]) // 2:]\n", "\t\t\taverage_second_half = np.mean(second_half)\n", "\n", "\t\t\tsecond_half_litos = litos_pce[i][len(litos_pce[i]) // 2:]\n", "\t\t\taverage_second_half_litos = np.mean(second_half_litos)\n", "\t\t\tif average_second_half > 2.5 and average_second_half_litos > 2.5:\n", "\t\t\t\taverage_sms.append(average_second_half)\n", "\t\t\t\taverage_litos.append(average_second_half_litos)\n", "\n", "\n", "\t\tlines = plt.gca().get_lines()\n", "\t\tx_min, x_max = plt.xlim()\n", "\t\tnum_lines = len(lines)\n", "\t\t# legend_anchor = (1, 0.2)\n", "\t\t# plt.legend(bbox_to_anchor=legend_anchor)\n", "\tplt.legend()\n", "\tplt.show()\n", "\n", "\n", "\tplt.figure(figsize=plot_size)\n", "\tbottom = min(np.min(v), np.min(litos_V))*0.95\n", "\ttop = max(np.max(v), np.max(litos_V))*1.05\n", "\tplt.ylim(bottom = bottom, top = top)\n", "\tplt.xlabel(time_label)\n", "\tplt.grid()\n", "\tplt.ylabel('Voltage (V)')\n", "\tplt.subplots_adjust(left=0.086,\n", "\t\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\t\tright=0.844,\n", "\t\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\t\thspace=0.2)\n", "\tcolors = plt.rcParams['axes.prop_cycle'].by_key()['color']\n", "\n", "\tfor i in plot_range:\n", "\t\tlineName = \"V\" + str(i + 1)\n", "\t\tif ss_file_path:\n", "\t\t\tplt.plot(time,\n", "\t\t\t\t\t v[:,i],\n", "\t\t\t\t\t \n", "\t\t\t\t\t color=colors[i % len(colors)],\n", "\t\t\t\t\t label = \"SMS\"+ str(i+1))\n", "\t\tif litos_directory:\n", "\t\t\tplt.plot(litos_time[i],\n", "\t\t\t\t\t litos_V[i],\n", "                     linestyle='--',\n", "\t\t\t\t\t color=colors[i % len(colors)],\n", "\t\t\t\t\t label = \"CAS\")\n", "\t# plt.legend(bbox_to_anchor=legend_anchor)\n", "\tplt.legend()\n", "\tplt.show()\n", "\n", "\tbottom = min(np.min(mA_density), np.min(litos_mA))*0.95\n", "\ttop = max(np.max(mA_density), np.max(litos_mA))*1.05\n", "\n", "\tplt.figure(figsize=plot_size)\n", "\tplt.ylim(bottom = bottom, top = top)\n", "\tplt.xlabel(time_label)\n", "\tplt.grid()\n", "\tplt.ylabel('Current Density (mA / cm^2)')\n", "\tplt.subplots_adjust(left=0.086,\n", "\t\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\t\tright=0.844,\n", "\t\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\t\thspace=0.2)\n", "\tcolors = plt.rcParams['axes.prop_cycle'].by_key()['color']\n", "\n", "\tfor i in plot_range:\n", "\t\tlineName = \"mA\" + str(i + 1)\n", "\t\tif ss_file_path:\n", "\t\t\tplt.plot(time,\n", "\t\t\t\t\t\tmA_density[:,i],\n", "\t\t\t\t\t\t\n", "\t\t\t\t\t\tcolor=colors[i % len(colors)],\n", "\t\t\t\t\t\tlabel = \"SMS\"+ str(i+1))\n", "\t\tif litos_directory:\n", "\t\t\tplt.plot(litos_time[i],\n", "\t\t\t\t\t\tlitos_mA[i],\n", "                        linestyle='--',\n", "\t\t\t\t\t\tcolor=colors[i % len(colors)],\n", "\t\t\t\t\t\tlabel = \"CAS\")\n", "\n", "\t# plt.legend(bbox_to_anchor=legend_anchor)\n", "\tplt.legend()\n", "\tplt.show()\n", "\n", "\tbox_plot = [average_sms, average_litos]\n", "\tlabels = [\"SMS\", \"Litos Lite\"]\n", "\n", "\t# Optional: compute stats using <PERSON><PERSON>lotlib's utility\n", "\tstats = boxplot_stats(box_plot)\n", "\n", "\taverage_sms = np.array(average_sms)\n", "\taverage_litos = np.array(average_litos)\n", "\tabs_diff = abs(average_sms - average_litos)\n", "\n", "\ttry:\n", "\t\tsms_outliers = get_outlier_indices(average_sms)\n", "\t\tlitos_outliers = get_outlier_indices(average_litos)\n", "\t\toutliers = np.unique(np.concatenate((sms_outliers, litos_outliers)))\n", "\texcept:\n", "\t\toutliers = []\n", "\tfiltered_sms = np.delete(average_sms, outliers)\n", "\tfiltered_litos = np.delete(average_litos, outliers)\n", "\tfiltered_diff = abs(filtered_sms-filtered_litos)\n", "\n", "\t# Plot the boxplot\n", "\t# if filtered:\n", "\t\t# box_plot = [filtered_sms, filtered_litos]\n", "\t# elif not filtered:\n", "\tbox_plot = [average_sms, average_litos]\n", "\tlabels = [\"SMS\", \"Litos Lite\"]\n", "\n", "\t# Optional: compute stats using <PERSON><PERSON>lotlib's utility\n", "\tstats = boxplot_stats(box_plot)\n", "\tfig, ax = plt.subplots(figsize=plot_size)\n", "\tbp = ax.boxplot(box_plot, flierprops=dict(marker='o', markersize=0))\n", "\tax.set_ylim(0, 25)\n", "\n", "\tfor i, stat in enumerate(stats, start=1):\n", "\t\toutliers = stat['fliers']  # already identified!\n", "\n", "\t\tif len(outliers) > 0:\n", "\t\t\tjitter = 0.2 * (np.random.rand(len(outliers)) - 0.5)  # random horizontal spread\n", "\t\t\tx_positions = np.full(len(outliers), i) + jitter\n", "\t\t\tax.scatter(x_positions, outliers, color='white', edgecolor='black', zorder=3)\n", "\n", "\t# Label the x-axis\n", "\tax.set_xticks([1, 2])\n", "\tax.set_title(\"SMS vs Litos PCE\")\n", "\tax.set_ylabel(\"PCE (%)\")\n", "\tax.set_xticklabels(labels)\n", "\tplt.show()\n", "\n", "\tif remove_outliers:\n", "\t\treturn filtered_diff, filtered_sms, filtered_litos\n", "\telse:\n", "\t\treturn abs_diff, average_sms, average_litos\n", "\n", "r\"\"\"\n", "Best\n", "\n", "litos search = _5_0\n", "\n", "for i in range(1,5):\n", "\tfiles.append([\n", "\t\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\April-03-2025 litos\\devices\\{i}\",\n", "\t\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-03-2025 19_41_27\\Apr-03-2025_19-57-33__ID{i}__mppt.csv\"\n", "\t])\n", "\n", "\"\"\"\n", "\n", "# sms_files = [rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\Apr-29-2025_17-27-31__ID1__mppt.csv\",\n", "#              rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\Apr-29-2025_17-33-16__ID2__mppt.csv\",\n", "#              rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\Apr-29-2025_17-33-16__ID3__mppt.csv\",\n", "#              rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-29-2025 17_25_13 leo devices\\Apr-29-2025_17-27-31__ID4__mppt.csv\"]\n", "\n", "\n", "sms_files = [\n", "    rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-30-2025 00_03_10 leo devices 2\\Apr-30-2025_00-04-23__ID1__mppt.csv\",\n", "\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-30-2025 00_03_10 leo devices 2\\Apr-30-2025_00-17-38__ID2__mppt.csv\",\n", "\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-30-2025 00_03_10 leo devices 2\\Apr-30-2025_00-17-38__ID3__mppt.csv\",\n", "    rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-30-2025 00_03_10 leo devices 2\\Apr-30-2025_00-04-23__ID4__mppt.csv\",\n", "\n", "             ]\n", "\n", "\n", "files = []\n", "for i in range(1,2):\n", "\tfiles.append([\n", "\t\t# rf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Apr-30-2025 00_03_10 leo devices 2\\devices\\{i}\",\n", "\t\t# sms_files[i-1]\n", "\t\t\"\",\n", "\t\trf\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\May-04-2025 11_18_15\\May-04-2025_11-21-10__ID{i}__compressedmppt.csv\",\n", "\t])\n", "\n", "plot_range = [x for x in range(4,5)]\n", "printouts = []\n", "all_diff = []\n", "average = []\n", "average_litos = []\n", "average_diff_device = []\n", "plot_size = (10,4)\n", "for litos, ss_file in files:\n", "\tabs_diff, temp_sms, temp_litos = plot_difference(litos, ss_file, plot_range, plot_size = plot_size, remove_outliers=False)\n", "\tsms = np.mean(temp_sms)\n", "\tlitos = np.mean(temp_litos)\n", "\tdevice_diff = abs(sms - litos)\n", "\taverage_diff_device.append(device_diff)\n", "\n", "\tall_diff.extend(abs_diff)\n", "\taverage.extend(temp_sms)\n", "\taverage_litos.extend(temp_litos)\n", "\t# print(\"sms:\" ,temp_sms)\n", "\t# print(\"lit:\" ,temp_litos)\n", "\tif litos and ss_file:\n", "\t\ttry:\n", "\t\t\tprintouts.append([np.min(abs_diff), np.argmin(abs_diff), np.mean(abs_diff), device_diff])\n", "\n", "\t\texcept:\n", "\t\t\tpass\n", "\n", "\n", "box_plot = [average, average_litos]\n", "labels = [\"SMS\", \"Litos Lite\"]\n", "\n", "# Optional: compute stats using <PERSON><PERSON>lotlib's utility\n", "stats = boxplot_stats(box_plot)\n", "fig, ax = plt.subplots(figsize=plot_size)\n", "bp = ax.boxplot(box_plot, flierprops=dict(marker='o', markersize=0))\n", "\n", "for i, stat in enumerate(stats, start=1):\n", "\toutliers = stat['fliers']  # already identified!\n", "\n", "\tif len(outliers) > 0:\n", "\t\tjitter = 0.2 * (np.random.rand(len(outliers)) - 0.5)  # random horizontal spread\n", "\t\tx_positions = np.full(len(outliers), i) + jitter\n", "\t\tax.scatter(x_positions, outliers, color='white', edgecolor='black', zorder=3)\n", "\n", "# Label the x-axis\n", "ax.set_xticks([1, 2])\n", "ax.set_title(\"SMS vs Litos PCE (All 4 Devices)\")\n", "ax.set_ylabel(\"PCE (%)\")\n", "ax.set_xticklabels(labels)\n", "plt.show()\n", "\n", "# # Print stats for each label\n", "# for label, stat in zip(labels, stats):\n", "#     print(f\"--- {label} ---\")\n", "#     print(f\"Min: {stat['whislo']}\")\n", "#     print(f\"Q1 : {stat['q1']}\")\n", "#     print(f\"Med: {stat['med']}\")\n", "#     print(f\"Q3 : {stat['q3']}\")\n", "#     print(f\"Max: {stat['whishi']}\")\n", "#     print(f\"Outliers: {stat['fliers']}\")\n", "#     print()\n", "\n", "for printout in printouts:\n", "\tprint(f\"min diff: {printout[0]} at idx: {printout[1]}\")  # or arr.min()\n", "\tprint(f\"Average PCE absolute difference: {printout[2]}\")\n", "\tprint(f\"Average PCE diff across device: {printout[3]}\")\n", "print(f\"total_diff {np.mean(all_diff)}\")\n", "print(f\"total device diff {np.mean(average_diff_device)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["errors = []\n", "for i in range(8):\n", "\tplt.figure(figsize=plot_size)\n", "\tplt.ylim(bottom = -0, top = max_pce)\n", "\tplt.xlabel('Time [min]')\n", "\tplt.grid()\n", "\n", "\tplt.ylabel('PCE [%]')\n", "\tplt.subplots_adjust(left=0.086,\n", "\t\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\t\tright=0.844,\n", "\t\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\t\thspace=0.2)\n", "\tpercent_error = float(\"inf\")\n", "\n", "\tlineName = \"PCE\" + str(i + 1)\n", "\tif ss_file_path:\n", "\t\tplt.plot(time,data[:,i], label = lineName)\n", "\tif litos_time:\n", "\t\tplt.plot(litos_time[i], litos_pce[i], label = \"litos\" + str(i+1))\n", "\tif ss_file_path and litos_time:\n", "\t\tsecond_half_litos = litos_pce[i][len(litos_pce[i]) // 2:]\n", "\t\taverage_second_half_litos = np.mean(second_half_litos)\n", "\n", "\t\tsecond_half = data[:,i][len(data[:,i]) // 2:]\n", "\t\taverage_second_half = np.mean(second_half)\n", "\n", "\t\tpercent_error = abs(average_second_half - average_second_half_litos) / abs(average_second_half_litos) * 100\n", "\n", "\tlabelLines(plt.gca().get_lines(), zorder=2.5)\n", "\tplt.title(\"Percent Error: \"+ str(percent_error))\n", "\tplt.legend(bbox_to_anchor=(1.15, 1))\n", "for idx, i in enumerate(errors):\n", "\tprint(idx+1, i)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "litos_directory = \"\"\n", "litos_directory = r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\Mar-4-2025 4 device test\\2025-03-04-- Litos New PCB 4 Device comparison\\devices\\device_4\"\n", "\n", "all_files = []\n", "\n", "if litos_directory != \"\":\n", "\tfor root, dirs, files in os.walk(litos_directory):\n", "\t\tfor file in files:\n", "\t\t\tif file.endswith('.csv'):\n", "\t\t\t\tfile_path = os.path.join(root, file)\n", "\t\t\t\tall_files.append(file_path)\n", "\n", "\n", "litos_pce = []\n", "litos_time = []\n", "\n", "# Process each file\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#plot litos uneven length\n", "\n", "litos_directory = \"\"\n", "litos_directory = r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\2024-11-06 --litos vs stability setup long test\\nov6_2024 test 1\\devices\"\n", "\n", "\n", "\n", "def process_files(litos_directory, all_files, pattern):\n", "\tall_files = []\n", "\n", "\tif litos_directory != \"\":\n", "\t\tfor root, dirs, files in os.walk(litos_directory):\n", "\t\t\tfor file in files:\n", "\t\t\t\tif file.endswith('.csv'):\n", "\t\t\t\t\tfile_path = os.path.join(root, file)\n", "\t\t\t\t\tall_files.append(file_path)\n", "\n", "\ttimes = []\n", "\tpces = []\n", "\tfor file_path in all_files:\n", "\t\tif pattern in file_path:\n", "\t\t\ttry:\n", "\t\t\t\tdata = np.genfromtxt(\n", "\t\t\t\t\tfile_path,\n", "\t\t\t\t\tdelimiter=',',\n", "\t\t\t\t\tcomments='#',\n", "\t\t\t\t\tskip_header=0,\n", "\t\t\t\t\tencoding='cp1252'  # Specify the correct encoding\n", "\t\t\t\t)\n", "\t\t\t\tpce = (data[:, 1] * data[:, 2] / 1000) / (0.1 * 0.128) * 100\n", "\t\t\t\ttimes.append(data[:, 0])\n", "\t\t\t\tpces.append(pce)\n", "\t\t\texcept Exception as e:\n", "\t\t\t\tprint(f\"Error processing {file_path}: {e}\")\n", "\n", "\tpces = pces[::-1]\n", "\treturn times, pces\n", "\n", "def calculate_second_half_averages(pces):\n", "\taverages = []\n", "\tfor pce in pces[:8]:  # Limit to first 8 files, as per the original code\n", "\t\tsecond_half = pce[len(pce) // 2:]\n", "\t\taverages.append(np.mean(second_half))\n", "\treturn averages\n", "\n", "# Process files for each pattern\n", "litos_time1, litos_pce1 = process_files(litos_directory, all_files, \"_3_0\")\n", "litos_time2, litos_pce2 = process_files(litos_directory, all_files, \"_7_0\")\n", "\n", "# Calculate averages for each pattern\n", "litos_beginning_avg = calculate_second_half_averages(litos_pce1)\n", "litos_ending_avg = calculate_second_half_averages(litos_pce2)\n", "\n", "print(litos_beginning_avg)\n", "print(litos_ending_avg)\n", "\n", "\n", "data = []\n", "\n", "ss_file_path = \"\"\n", "ss_file_path = r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\2024-11-06 --litos vs stability setup long test\\Nov-06-2024 13_50_53\\Nov-06-2024 13_50_53ID2PnO.csv\"\n", "if ss_file_path != \"\":\n", "\tarr = np.loadtxt(ss_file_path,\n", "\t\t\t\t\t\tdelimiter=\",\",\n", "\t\t\t\t\t\tdtype=str)\n", "\n", "\n", "\theaders = arr[6,:]\n", "\theader_dict = {value: index for index, value in enumerate(headers)}\n", "\tpce_indicies = [header_dict[value] for value in header_dict if \"PCE\" in value]\n", "\tarr = arr[7:, :]\n", "\n", "\ttime = np.array(arr[:,header_dict[\"Time\"]]).astype('float')\n", "\ttime/=60\n", "\tpce_list = np.array(arr)\n", "\tpce_list = pce_list[:, pce_indicies]\n", "\t# pce_list = pce_list[:,0:-1]\n", "\tfor i in range(len(pce_list)):\n", "\t\tpce_list[i] = [float(j) if j != \" ovf\" else 0.0 for j in pce_list[i]]\n", "\t\tpce_list[i] = [float(j) if j != \"nan\" else 0.0 for j in pce_list[i]]\n", "\n", "\tpce_list = pce_list.astype(float)\n", "\n", "\tdata = pce_list #np.array(data).T\n", "\t# data *= 2.048 # comment line if not using mask\n", "\n", "# min_time = min(time)*0.99\n", "# max_time = max(time)*1.01\n", "plot_size = (12,8)\n", "min_pce = 0\n", "max_pce = 15\n", "\n", "plt.figure(figsize=plot_size)\n", "# plt.xlim(min_time,max_time)\n", "plt.ylim(bottom = min_pce, top = max_pce)\n", "plt.xlabel('Time [min]')\n", "plt.grid()\n", "\n", "plt.ylabel('PCE [%]')\n", "plt.subplots_adjust(left=0.086,\n", "\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\tright=0.844,\n", "\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\thspace=0.2)\n", "\n", "errors = []\n", "\n", "for i in range(8):\n", "\tlineName = \"PCE\" + str(i + 1)\n", "\t# print(np.array(pce_list[i]))\n", "\tif data != []:\n", "\t\tplt.plot(time,data[:,i], label = lineName)\n", "\tif i < len(litos_beginning_avg) and i < len(litos_ending_avg):\n", "\t\t# Calculate straight line\n", "\t\ty_values = [\n", "\t\t\tlitos_beginning_avg[i],\n", "\t\t\tlitos_ending_avg[i]\n", "\t\t]\n", "\t\tprint(y_values)\n", "\t\tx_values = [time[0], time[-1]]\n", "\t\tplt.plot(x_values, y_values, linestyle=\"--\", label=f\"Litos {i + 1}\")\n", "\n", "print(np.mean(errors))\n", "\n", "lines = plt.gca().get_lines()\n", "x_min, x_max = plt.xlim()\n", "num_lines = len(lines)\n", "xvals = np.linspace(x_min + 0.1 * (x_max - x_min), x_max - 0.1 * (x_max - x_min), num_lines)\n", "bold_font = FontProperties(weight='medium')\n", "labelLines(\n", "\tlines,\n", "\txvals=xvals,\n", "\tzorder=2.5,\n", "\talign=False,\n", "\tfontsize=11,\n", "\tfontproperties=bold_font\n", ")\n", "plt.legend(bbox_to_anchor=(1.15, 1))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["errors = []\n", "for i in range(8):\n", "\tplt.figure(figsize=plot_size)\n", "\tplt.ylim(bottom = -0, top = max_pce)\n", "\tplt.xlabel('Time [min]')\n", "\tplt.grid()\n", "\n", "\tplt.ylabel('PCE [%]')\n", "\tplt.subplots_adjust(left=0.086,\n", "\t\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\t\tright=0.844,\n", "\t\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\t\thspace=0.2)\n", "\n", "\n", "\tlineName = \"PCE\" + str(i + 1)\n", "\t# print(np.array(pce_list[i]))\n", "\tplt.plot(time,data[:,i], label = lineName)\n", "\tif i < len(litos_beginning_avg) and i < len(litos_ending_avg):\n", "\t\t# Calculate straight line\n", "\t\ty_values = [\n", "\t\t\tlitos_beginning_avg[i],\n", "\t\t\tlitos_ending_avg[i]\n", "\t\t]\n", "\t\tx_values = [time[0], time[-1]]\n", "\t\tplt.plot(x_values, y_values, linestyle=\"--\", label=f\"Litos {i + 1}\")\n", "\n", "\tlast_portion = data[:,i][99*len(data[:,i]) // 100:]\n", "\taverage_last_ss = np.mean(last_portion)\n", "\n", "\tpercent_error = abs(average_last_ss - litos_ending_avg[i]) / abs(litos_ending_avg[i]) * 100\n", "\terrors.append(percent_error)\n", "\n", "\tlabelLines(plt.gca().get_lines(), zorder=2.5)\n", "\tplt.title(\"Per<PERSON>r for final result: \"+ str(percent_error))\n", "\tplt.legend(bbox_to_anchor=(1.15, 1))\n", "\n", "\n", "for idx, i in enumerate(errors):\n", "\tprint(idx+1, i)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_litos_only(litos_directory, pattern):\n", "\t# litos_directory = \"\"\n", "\n", "\tall_files = []\n", "\n", "\tif litos_directory != \"\":\n", "\t\tfor root, dirs, files in os.walk(litos_directory):\n", "\t\t\tfor file in files:\n", "\t\t\t\tif file.endswith('.csv'):\n", "\t\t\t\t\tfile_path = os.path.join(root, file)\n", "\t\t\t\t\tall_files.append(file_path)\n", "\n", "\n", "\tlitos_pce = []\n", "\tlitos_time = []\n", "\n", "\t# Process each file\n", "\tfor file_path in all_files:\n", "\t\tif pattern in file_path:\n", "\t\t\ttry:\n", "\t\t\t\tdata = np.genfromtxt(\n", "\t\t\t\t\tfile_path,\n", "\t\t\t\t\tdelimiter=',',\n", "\t\t\t\t\tcomments='#',\n", "\t\t\t\t\tskip_header=0,\n", "\t\t\t\t\tencoding='cp1252'  # Specify the correct encoding\n", "\t\t\t\t)\n", "\t\t\t\tpce = (data[:, 1] * data[:, 2] /1000) / (0.1*0.128)*100\n", "\t\t\t\t# data_with_pce= np.column_stack((data, pce))\n", "\t\t\t\tlitos_time.append(data[:,0])\n", "\t\t\t\tlitos_pce.append(pce)\n", "\t\t\texcept Exception as e:\n", "\t\t\t\tprint(f\"Error processing {file_path}: {e}\")\n", "\n", "\tlitos_pce = litos_pce[::-1]\n", "\tlitos_time = litos_time[::-1]\n", "\n", "\n", "\tplt.figure(figsize=plot_size)\n", "\tplt.xlabel('Time [sec]')\n", "\tplt.grid()\n", "\n", "\tplt.ylabel('PCE [%]')\n", "\tplt.subplots_adjust(left=0.086,\n", "\t\t\t\t\t\tbottom=0.06,\n", "\t\t\t\t\t\tright=0.844,\n", "\t\t\t\t\t\ttop=0.927,\n", "\t\t\t\t\t\twspace=0.2,\n", "\t\t\t\t\t\thspace=0.2)\n", "\n", "\terrors = []\n", "\n", "\tfor i in range(8):\n", "\t\tif litos_time:\n", "\t\t\tplt.plot(litos_time[i], litos_pce[i], label = \"litos\" + str(i+1))\n", "\t\t\tsecond_half_litos = litos_pce[i][len(litos_pce[i]) // 2:]\n", "\t\t\taverage_second_half_litos = np.mean(second_half_litos)\n", "\t\t\tprint(average_second_half_litos)\n", "\n", "\tprint(np.mean(errors))\n", "\n", "\tlines = plt.gca().get_lines()\n", "\tx_min, x_max = plt.xlim()\n", "\tnum_lines = len(lines)\n", "\txvals = np.linspace(x_min + 0.1 * (x_max - x_min), x_max - 0.1 * (x_max - x_min), num_lines)\n", "\tbold_font = FontProperties(weight='medium')\n", "\tlabelLines(\n", "\t\tlines,\n", "\t\txvals=xvals,\n", "\t\tzorder=2.5,\n", "\t\talign=False,\n", "\t\tfontsize=11,\n", "\t\tfontproperties=bold_font\n", "\t)\n", "\tplt.legend(bbox_to_anchor=(1.15, 1))\n", "plot_litos_only(r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\2024-11-06 --litos vs stability setup long test\\nov6_2024 test 1\\devices\", \"_3_0\")\n", "plot_litos_only(r\"C:\\Users\\<USER>\\Dropbox\\code\\Stability-Setup\\data\\2024-11-06 --litos vs stability setup long test\\nov6_2024 test 1\\devices\", \"_7_0\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 4}